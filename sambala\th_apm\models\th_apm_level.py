from odoo import fields, models, api, exceptions

class APMLevel(models.Model):
    _name = "th.apm.level"
    _description = "Mối quan hệ"
    _rec_name = "name"
    _order = 'th_sequence asc'

    name = fields.Char(string="Tên", required=True)
    th_sequence = fields.Integer(string="", default=1)
    th_last_status = fields.<PERSON>olean(string="Trạng thái cuối")
    th_first_status = fields.Boolean(string="Trạng thái đầu")
    th_status_after_sales_care = fields.Boolean(string="Trạng thái sau bán hàng")
    th_description = fields.Char(string="<PERSON><PERSON> tả")
    is_after_sale = fields.Boolean(string="mối quan hệ cơ hội sau bán")
    th_vstep_status = fields.Boolean(string="Trạng vstep")
    th_can_create_order = fields.<PERSON><PERSON>an(string="Cho phép tạo đơn hàng",default= False, help="<PERSON><PERSON> t<PERSON><PERSON> chọn, c<PERSON> hội ở level này sẽ hiển thị nút tạo đơn hàng")

    @api.constrains('th_last_status')
    def _check_last_status_unique_to_type(self):
        for rec in self:
            if rec.th_last_status and self.search([('th_last_status', '=', True), ('id', '!=', rec.id)], limit=1):
                raise exceptions.ValidationError('Chỉ được có một trạng thái đầu và một trạng thái cuối')

    @api.constrains('th_first_status')
    def _check_first_status_unique_to_type(self):
        for rec in self:
            if rec.th_first_status and self.search([('th_first_status', '=', True), ('id', '!=', rec.id)], limit=1):
                raise exceptions.ValidationError('Chỉ được có một trạng thái đầu và một trạng thái cuối')

    @api.constrains('name')
    def _check_name_uniq(self):
        for rec in self:
            if self.search_count([('name', '=', rec.name), ('id', '!=', rec.id)]) > 0:
                raise exceptions.ValidationError("Tên %s đã tồn tại." %rec.name)

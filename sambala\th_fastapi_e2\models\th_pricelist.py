from odoo import fields, models, api
from odoo.exceptions import ValidationError, UserError
import requests
from datetime import datetime
import json


class ProductPricelist(models.Model):
    _inherit = 'product.pricelist'
    # pricelist_type = fields.Selection(selection=[('vmc', 'VMC'), ('vstep', 'Vstep')], string='Loại bảng giá')

    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })
    @api.model_create_multi
    def create(self, vals_list):
        res = super(ProductPricelist, self).create(vals_list)
        for rec in res:
            if not rec.th_e2_product_price_list_id and res.th_origin_id.id in (
                self.env.ref('th_setup_parameters.th_origin_vstep').id,
                self.env.ref('th_setup_parameters.th_origin_vmc').id
            ) and not self._context.get('th_test_import', False):
                data= {
                    'name': rec.name,
                    'active': rec.active,
                    'sequence': rec.sequence,
                    'pricelist_sambala_id': rec.id,
                    'pricelist_type': "vmc" if res.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vmc').id else "vstep",
                    'currency_id': rec.currency_id.id,
                }
                rec.th_create_pricelist(data)
        return res

    def th_create_pricelist(self, data):
        """
        Gửi request tạo danh sách giá (pricelist) trên hệ thống 2E thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'th_create_pricelist')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_pricelist"

        try:
            response = requests.post(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                self.with_context(th_sync=True).write({
                    'th_e2_product_price_list_id': response_data.get('th_pricelist_id')
                })
                msg = "✅ Tạo danh sách giá thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, data, 'th_create_pricelist')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'th_create_pricelist')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'th_create_pricelist')
            return {"status_code": 500, "message": msg}

    def write(self, vals):
        res = super(ProductPricelist, self).write(vals)
        for rec in self:
            if rec.th_e2_product_price_list_id  and not self._context.get('th_test_import', False) and not self._context.get('th_sync', False):
                data = {
                    'name': rec.name,
                    'active': rec.active,
                    'sequence': rec.sequence,
                    'pricelist_sambala_id': rec.id,
                    'pricelist_type': "vmc" if rec.th_origin_id.id == self.env.ref(
                        'th_setup_parameters.th_origin_vmc').id else "vstep",
                    'currency_id': rec.currency_id.id,
                }
                rec.th_update_pricelist(data,rec.id)
        return res

    def th_update_pricelist(self, data, pricelist_sambala_id):
        """
        Gửi request cập nhật danh sách giá (pricelist) trên hệ thống 2E thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'th_update_pricelist')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_pricelist/{pricelist_sambala_id}"

        try:
            response = requests.put(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = "✅ Cập nhật danh sách giá thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, data, 'th_update_pricelist')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'th_update_pricelist')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'th_update_pricelist')
            return {"status_code": 500, "message": msg}

    def unlink(self):
        """
        Gọi API xóa bảng trên hệ thống 2E trước khi xóa trong Odoo.
        """
        # Gọi API xóa trước khi thực hiện unlink()
        for sambala_id in self:
            if sambala_id.th_e2_product_price_list_id:  # Chỉ gọi API nếu có product_sambala_id hợp lệ
                self.th_delete_price_list(sambala_id.id)
        # Xóa bản ghi trong Odoo
        return super().unlink()

    def th_delete_price_list(self, pricelist_sambala_id):
        """
        Gửi request xóa danh sách giá (pricelist) trên hệ thống 2E thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, pricelist_sambala_id, 'th_delete_price_list')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_pricelist/{pricelist_sambala_id}"

        try:
            response = requests.delete(url, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = "✅ Xóa danh sách giá thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, pricelist_sambala_id, 'th_delete_price_list')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, pricelist_sambala_id, 'th_delete_price_list')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, pricelist_sambala_id, 'th_delete_price_list')
            return {"status_code": 500, "message": msg}


class ProductPricelistItem(models.Model):
    _inherit = 'product.pricelist.item'

    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })
    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        if res.pricelist_id.th_e2_product_price_list_id:
            for rec in res:
                if  not self._context.get('th_test_import', False):
                    data = {
                        'pricelist_item_sambala_id': rec.id,
                        'pricelist_sambala_id': rec.pricelist_id.id,
                        'product_sambala_id': rec.product_tmpl_id.id,
                        'display_applied_on': rec.applied_on,
                        'base': rec.base,
                        'compute_price': rec.compute_price,
                        'min_quantity': rec.min_quantity,
                        'fixed_price': rec.fixed_price,
                        'price_discount': rec.price_discount,
                        'price_round': rec.price_round,
                        'price_surcharge': rec.price_surcharge,
                        'price_min_margin': rec.price_min_margin,
                        'price_max_margin': rec.price_max_margin,
                        'percent_price': rec.price_max_margin,
                    }
                    if rec.date_start:
                        data["start_date"] = rec.date_start.strftime("%Y-%m-%d %H:%M:%S")
                    if rec.date_end:
                        data["thc_date_to"] = rec.date_end.strftime("%Y-%m-%d %H:%M:%S")
                    if rec.price_min_margin:
                        data['price_min_margin']: rec.price_min_margin
                    if rec.price_max_margin:
                        data['price_max_margin']: rec.price_max_margin
                    if rec.percent_price:
                        data['percent_price']: rec.percent_price
                rec.create_product_price_listitem(data)
        return res

    def create_product_price_listitem(self, data):
        """
        Gửi dữ liệu danh sách giá sản phẩm đến API hệ thống 2E để tạo mới.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'create_product_price_listitem')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_pricelist_item"

        try:
            response = requests.post(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                self.with_context(th_sync=True).write({
                    'th_e2_product_pricelist_item_id': response_data.get('th_pricelist_item_id')
                })
                msg = "✅ Tạo danh sách giá sản phẩm thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, data, 'create_product_price_listitem')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'create_product_price_listitem')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'create_product_price_listitem')
            return {"status_code": 500, "message": msg}

    def write(self, values):
        # Add code here
        if not self._context.get('no_update'):
            values['th_synchronized'] = False
        res = super(ProductPricelistItem, self).write(values)
        for rec in self:
            if rec.th_e2_product_pricelist_item_id and not self._context.get('th_test_import', False) and not self._context.get('th_sync', False):
                data = {
                    'pricelist_item_sambala_id': rec.id,
                    'pricelist_sambala_id': rec.pricelist_id.id,
                    'product_sambala_id': rec.product_tmpl_id.id,
                    'display_applied_on': rec.applied_on,
                    'base': rec.base,
                    'compute_price': rec.compute_price,
                    'min_quantity': rec.min_quantity,
                    'fixed_price': rec.fixed_price,
                    'price_discount': rec.price_discount,
                    'price_round': rec.price_round,
                    'price_surcharge': rec.price_surcharge,
                    'price_min_margin': rec.price_min_margin,
                    'price_max_margin': rec.price_max_margin,
                    'percent_price': rec.price_max_margin,
                }
                if rec.date_start:
                    data["start_date"] = rec.date_start.strftime("%Y-%m-%d %H:%M:%S")
                if rec.date_end:
                    data["thc_date_to"] = rec.date_end.strftime("%Y-%m-%d %H:%M:%S")
                if rec.price_min_margin:
                    data['price_min_margin']: rec.price_min_margin
                if rec.price_max_margin:
                    data['price_max_margin']: rec.price_max_margin
                if rec.percent_price:
                    data['percent_price']: rec.percent_price
                rec.update_product_price_listitem(data,rec.id)
        return res

    def update_product_price_listitem(self, data, item_id):
        """
        Gửi dữ liệu cập nhật danh sách giá sản phẩm đến API hệ thống 2E.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'update_product_price_listitem')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_pricelist_item/{item_id}"

        try:
            response = requests.put(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = "✅ Cập nhật danh sách giá sản phẩm thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, data, 'update_product_price_listitem')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'update_product_price_listitem')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'update_product_price_listitem')
            return {"status_code": 500, "message": msg}

    def unlink(self):
        for rec in self:
            if rec.th_e2_product_pricelist_item_id:
                rec.th_delete_product_item_list(rec)
        res = super().unlink()
        self.clear_caches()
        return res

    def th_delete_product_item_list(self, rec):
        """
        Gọi API xóa danh sách giá sản phẩm trên hệ thống 2E.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, rec.id, 'th_delete_product_item_list')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_pricelist_item/{rec.id}"

        try:
            response = requests.delete(url, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = f"✅ Xóa danh sách giá sản phẩm thành công! (ID: {rec.id})"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, rec.id, 'th_delete_product_item_list')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, rec.id, 'th_delete_product_item_list')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, rec.id, 'th_delete_product_item_list')
            return {"status_code": 500, "message": msg}




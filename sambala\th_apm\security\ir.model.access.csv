id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink

access_th_apm,access_th_apm,model_th_apm,,1,0,0,0

access_apm_user,access_apm_user,model_th_apm,th_apm.group_apm_user,1,1,1,0

access_apm_leader,access_apm_leader,model_th_apm,th_apm.group_apm_leader,1,1,1,0

access_apm_administrator,access_apm_administrator,model_th_apm,th_apm.group_apm_administrator,1,1,1,1

access_th_apm_team,access_th_apm_team,model_th_apm_team,,1,1,1,1

access_th_apm_level_admin,access_th_apm_level,model_th_apm_level,,1,1,1,1

access_th_apm_need,access_th_apm_need,model_th_apm_need,,1,1,1,1

access_th_apm_reason,access_th_apm_reason,model_th_apm_reason,,1,1,1,1

access_th_apm_trait,access_th_apm_trait,model_th_apm_trait,,1,1,1,1

access_th_apm_trait_value,access_th_apm_trait_value,model_th_apm_trait_value,,1,1,1,1

access_th_apm_contact_trait,access_th_apm_contact_trait,model_th_apm_contact_trait,,1,1,1,1

access_th_apm_campaign,access_th_apm_campaign,model_th_apm_campaign,,1,1,1,1

access_th_apm_lead_trait,access_th_apm_lead_trait,model_th_apm_lead_trait,,1,1,1,1
access_th_apm_student_status,access_th_apm_student_status,model_th_apm_student_status,,1,1,1,1
access_th_apm_resign,access_th_apm_resign,model_th_apm_resign,,1,1,1,1
access_th_detail_apm_resign,access_th_detail_apm_resign,model_th_detail_apm_resign,,1,1,1,1
access_th_processing_status,access_th_processing_status,model_th_processing_status,,1,1,1,1

access_apm_assign_leads,access_apm_assign_leads,model_apm_assign_leads,,1,1,1,1
access_apm_after_order,access_apm_after_order,model_th_apm,th_apm.group_apm_after_order,1,1,1,0
access_apm_sale_order,access_apm_sale_order,model_sale_order,th_apm.group_apm_after_order,1,1,1,0
access_apm_sale_order_line,access_apm_sale_order_line,model_sale_order_line,th_apm.group_apm_after_order,1,1,1,1
access_th_apm_opportunity_list_partner,access_th_apm_opportunity_list_partner,model_th_apm_opportunity_list_partner,,1,1,1,1
access_th_order_history,access_th_order_history,model_th_order_history,,1,1,1,1
access_th_refund_invoice_apm,access_th_refund_invoice_apm,model_th_refund_invoice_apm,,1,1,1,1

access_import_student_status_wizard,access_import_student_status_wizard,model_import_student_status_wizard,,1,1,1,1

access_report_th_apm_sample_import_student_status,access_report_th_apm_sample_import_student_status,model_report_th_apm_sample_import_student_status,,1,1,1,1
access_th_apm_active_account,access_th_apm_active_account,model_th_apm_active_account,,1,1,1,1
access_th_apm_active_account_line,access_th_apm_active_account_line,model_th_apm_active_account_line,,1,1,1,1
access_th_apm_dividing_ring,access_th_apm_dividing_ring,model_th_apm_dividing_ring,,1,1,1,1
access_th_cohort,access_th_th_cohort,model_th_cohort,,1,1,1,1
access_th_apm_check_condition,th_apm.th_apm_check_condition,model_th_apm_check_condition,,1,1,1,1
from typing import Annotated
from odoo.exceptions import UserError
from ..schemas import ApmTraitDatas, RecordDatas,APMLeadDatas
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path
from ..dependencies import authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
import time

router = APIRouter(tags=["apm"])


def write_log(self, data: object, state: str, duration: str, function_name: str = None, description: str = None):
    self.env['th.log.api'].create({
        'state': state,
        'th_model': str(self._name),
        'th_description': description,
        'th_input_data': str(data),
        'th_function_call': function_name,
        'is_log_fast_api': True,
        'th_fastapi_endpoint_id': self.id,
        'th_time_response': duration,
    })


@router.post("/api/apmleads/{id_b2b}")
def create_apm_lead(
    ApmLeadDatas: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint), ], background_tasks: BackgroundTasks,
    id_b2b: int = Path(..., ge=1),
):
    values_b2b = {}
    start = time.perf_counter()
    try:
        if fastapi:
            results = []
            for record in ApmLeadDatas:
                values_b2b = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                partner_b2b_id = values_b2b.get('th_partner_id', None)
                partner_b2b_info = values_b2b.get('partner_info').copy() if values_b2b.get('partner_info') else None
                # val_samp
                values = fastapi.env['th.apm'].map_data_apm_from_sync(values_b2b)
                partner_info = values.pop('partner_info') if values_b2b.get('partner_info') else None
                mapid = fastapi.env['th.mapping.id'].search([
                    ('th_external_id', '=', id_b2b),
                    ('th_model_name', '=', 'th.apm'),
                    ('th_system', '=', 'b2b')
                ], limit=1)
                partner_sam = fastapi.env['th.mapping.id'].search([
                    ('th_external_id', '=', partner_b2b_id),
                    ('th_model_name', '=', 'res.partner'),
                    ('th_system', '=', 'b2b')
                ], limit=1)
                if partner_sam:
                    values['th_partner_id'] = partner_sam.th_internal_id
                if mapid and mapid.th_internal_id: # write
                    results = []
                    response = fastapi.env['th.apm'].browse(mapid.th_internal_id).sudo().with_context(th_sync=True).th_write_th_apm(values)
                    results.append({
                        "status": "success",
                        "response": response,
                        "id": record.id_b2b,
                    })
                    background_tasks.add_task(write_log, fastapi, values_b2b, 'success', str(round(time.perf_counter() - start, 4)), 'api/apmleads')
                    return results
                else: # create
                    partner_map = None
                    # chưa có partner
                    if not values['th_partner_id'] and partner_info:
                        if partner_info['country_id']:
                            country = fastapi.env['res.country'].search([('code', '=', partner_info['country_id'])],    limit=1)
                            partner_info['country_id'] = country.id
                        if partner_info['th_country_id']:
                            th_country = fastapi.env['res.country'].search([('code', '=', partner_info['th_country_id'])], limit=1)
                            partner_info['th_country_id'] = th_country.id
                        values_partner = fastapi.env['res.partner'].map_data_res_partner_from_sync(partner_info)
                        partner = fastapi.env['res.partner'].create(values_partner)
                        values['th_partner_id'] = partner.id
                        partner_map = partner
                    apm_lead = fastapi.env['th.apm'].sudo().with_context(th_sync=True).th_create_th_apm(values)
                    if apm_lead:
                        fastapi.env['th.mapping.id'].create({
                            'name': apm_lead.name,
                            'th_model_name': 'th.apm',
                            'th_internal_id': apm_lead.id,
                            'th_external_id': id_b2b,
                            'th_system': 'b2b',
                            'th_module_id': fastapi.env.ref('th_setup_parameters.th_apm_module').id,
                        })
                        if partner_map:
                            fastapi.env['th.mapping.id'].create({
                                'name': partner_map.name,
                                'th_model_name': 'res.partner',
                                'th_internal_id': partner_map.id,
                                'th_external_id': partner_b2b_id,
                                'th_system': 'b2b',
                            })

                    results.append({
                        'apm_lead': {'name': apm_lead.name},
                        'partner_id': {}
                    })
                    if not partner_b2b_info.get('th_customer_code', None):
                        for result in results:
                            result['partner_id']['th_customer_code'] = apm_lead.th_customer_code
                    background_tasks.add_task(write_log, fastapi, values_b2b, 'success', str(round(time.perf_counter() - start, 4)), 'api/apmleads')
                    print(round(time.perf_counter() - start, 4))
                    return results

    except Exception as e:
        write_log(fastapi, values_b2b, 'error', str(round(time.perf_counter() - start, 4)), 'api/apmleads', str(e))
        fastapi.env.cr.commit()
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/apmleads/{id_b2b}")
def delete_apm_lead(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=1)
):
    try:
        if fastapi:
            mapid = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id_b2b),
                ('th_model_name', '=', 'th.apm'),
                ('th_system', '=', 'b2b')
            ], limit=1)

            if not mapid or not mapid.th_internal_id:
                raise HTTPException(
                    status_code=404,
                    detail="Không tìm thấy bản ghi tương ứng trong SamP."
                )

            apm_lead = fastapi.env['th.apm'].browse(mapid.th_internal_id)
            if not apm_lead.exists():
                raise HTTPException(
                    status_code=404,
                    detail="Không tìm thấy cơ hội APM trong SamP."
                )

            apm_lead.sudo().with_context(th_sync=True).unlink()
            mapid.sudo().unlink()

            return {
                "message": "Đã xóa cơ hội APM thành công.",
                "id_b2b": id_b2b
            }

    except UserError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Lỗi người dùng: {str(e)}"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@router.post("/api/thapmtrait/{id_b2b}")
def create_apm_trait(
    ApmTraitDatas: ApmTraitDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=0)
):
    try:
        if fastapi:
            values_b2b = ApmTraitDatas.model_dump(exclude_none=True, exclude_unset=True)
            values = fastapi.env['th.apm.trait'].map_data_apm_trait_from_sync(values_b2b)  # val_samp
            mapid = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id_b2b),
                ('th_model_name', '=', 'th.apm.trait'),
                ('th_system', '=', 'b2b')
            ], limit=1)

            if mapid and mapid.th_internal_id:  # write
                fastapi.env['th.ap.trait'].browse(mapid.th_internal_id).sudo().with_context(th_sync=True).write(values)
            else:  # create
                apm_trait = fastapi.env['th.apm.trait'].sudo().with_context(th_sync=True).create(values)
                if apm_trait:
                    fastapi.env['th.mapping.id'].create({
                        'name': apm_trait.name,
                        'th_model_name': 'th.apm.trait',
                        'th_internal_id': apm_trait.id,
                        'th_external_id': id_b2b,
                        'th_system': 'b2b',
                        'th_module_id': fastapi.env.ref('th_setup_parameters.th_apm_module').id,
                    })

                result = {
                    'apm_lead': {
                        'name': apm_trait.name,
                    },
                }
                return result

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

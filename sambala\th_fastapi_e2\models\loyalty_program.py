from odoo import api, fields, models, _
from xml.etree import ElementTree as etree
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import requests
import json

class LoyaltyProgram(models.Model):
    _inherit = 'loyalty.program'
    _description = "Mã giảm giá"

    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })
    @api.model_create_multi
    def create(self, vals_list):
        res = super(LoyaltyProgram, self).create(vals_list)
        if not self._context.get('th_test_import', False):
            for item in res:
                loyal_rule = []
                for rule in item.rule_ids:
                    loyal_rule.append({
                        "rule_sambala_id": rule.id,
                        "product_category_sambala_id": rule.product_category_id.id,
                        "product_sambala_ids": rule.product_ids.mapped("default_code"),
                        "minimum_qty": rule.minimum_qty,
                        "minimum_amount": rule.minimum_amount,
                        "product_domain": rule.product_domain,
                        "reward_point_mode": rule.reward_point_mode,
                        "mode": rule.mode,
                        "code": rule.code if rule.code else "",
                        "reward_point_split": rule.reward_point_split,
                        "reward_point_amount": rule.reward_point_amount
                    })

                # Chuyển đổi loyalty rewards thành danh sách dictionary hợp lệ
                loyal_reward = []
                for reward in item.reward_ids:
                    loyal_reward.append({
                        "reward_sambala_id": reward.id,
                        "condition_des": reward.condition_des,
                        "reward_type": reward.reward_type,
                        "discount_mode": reward.discount_mode,
                        "discount": reward.discount,
                        "discount_max_amount": reward.discount_max_amount,
                        "discount_applicability": reward.discount_applicability,
                        "discount_product_domain": reward.discount_product_domain,
                        "reward_product_ids": reward.reward_product_id.id if reward.reward_product_id else False,
                        "required_points": reward.required_points,
                        "description": reward.description
                    })

                # Chuẩn bị dữ liệu để gửi đi
                data_to_send = {
                    "loyal_sambala_id": item.id,
                    "type_product": "vmc" if item.th_origin_id.id == self.env.ref(
                        "th_setup_parameters.th_origin_vmc").id else "vstep",
                    "name": item.name,
                    "program_type": item.program_type,
                    "sale_ok": item.sale_ok,
                    "trigger": item.trigger,
                    "limit_usage": item.limit_usage,
                    "is_preorder": item.is_preorder,
                    "max_usage": item.max_usage,
                    "ecommerce_ok": item.ecommerce_ok,
                    "loyalty_rewards": loyal_reward,  # Danh sách dictionary hợp lệ
                    "loyalty_rules": loyal_rule,  # Danh sách dictionary hợp lệ
                    "active": item.active,
                }
                if item.date_to:
                    data_to_send["date_to"] = item.date_to.strftime("%Y-%m-%d")
                if item.start_date:
                    data_to_send["start_date"] = item.start_date.strftime("%Y-%m-%d %H:%M:%S")
                if item.thc_date_to:
                    data_to_send["thc_date_to"] = item.thc_date_to.strftime("%Y-%m-%d %H:%M:%S")
            item.th_create_loyalty(data_to_send)
        return res

    def th_create_loyalty(self, data):
        """
        Hàm tạo chương trình loyalty trên hệ thống 2E thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'th_create_loyalty')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/loyalty_program"

        try:
            response = requests.post(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                self.with_context(th_sync=True).write({
                    'th_2e_discount_loyalty_id': response_data.get('loyalty_program_e2_id')
                })
                msg = "✅ Tạo chương trình loyalty thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, data, 'th_create_loyalty')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'th_create_loyalty')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'th_create_loyalty')
            return {"status_code": 500, "message": msg}

    def write(self, values):
        # Add code here
        res = super(LoyaltyProgram, self).write(values)
        for item in self:
            if item.th_2e_discount_loyalty_id and not self._context.get('th_test_import', False) and not self._context.get('th_sync', False):
                loyal_rule = []
                for rule in item.rule_ids:
                    # Lấy data loyalty rule
                    loyal_rule.append({
                        "rule_sambala_id": rule.id,
                        "product_category_sambala_id": rule.product_category_id.id,
                        "product_sambala_ids": rule.product_ids.mapped("default_code"),
                        "minimum_qty": rule.minimum_qty,
                        "minimum_amount": rule.minimum_amount,
                        "product_domain": rule.product_domain,
                        "reward_point_mode": rule.reward_point_mode,
                        "mode": rule.mode,
                        "code": rule.code if rule.code else "",
                        "reward_point_split": rule.reward_point_split,
                        "reward_point_amount": rule.reward_point_amount
                    })

                # Lấy data loyalty rewards
                loyal_reward = []
                for reward in item.reward_ids:
                    loyal_reward.append({
                        "reward_sambala_id": reward.id,
                        "condition_des": reward.condition_des,
                        "reward_type": reward.reward_type,
                        "discount_mode": reward.discount_mode,
                        "discount": reward.discount,
                        "discount_max_amount": reward.discount_max_amount,
                        "discount_applicability": reward.discount_applicability,
                        "discount_product_domain": reward.discount_product_domain,
                        "reward_product_ids":  reward.reward_product_id.id if reward.reward_product_id and isinstance(reward.reward_product_id.id, int) else None,
                        "required_points": reward.required_points,
                        "description": reward.description
                    })

                # Chuẩn bị dữ liệu để gửi đi
                data_to_send = {
                    "loyal_sambala_id": item.id,
                    "type_product": "vmc" if item.th_origin_id.id == self.env.ref(
                        "th_setup_parameters.th_origin_vstep").id else "vstep",
                    "name": item.name,
                    "program_type": item.program_type,
                    "sale_ok": item.sale_ok,
                    "trigger": item.trigger,
                    "limit_usage": item.limit_usage,
                    "is_preorder": item.is_preorder,
                    "max_usage": item.max_usage,
                    "ecommerce_ok": item.ecommerce_ok,
                    "loyalty_rewards": loyal_reward,  # Danh sách dictionary hợp lệ
                    "loyalty_rules": loyal_rule,  # Danh sách dictionary hợp lệ
                    "active": item.active,
                }
                if item.date_to:
                    data_to_send["date_to"] = item.date_to.strftime("%Y-%m-%d")
                if item.start_date:
                    data_to_send["start_date"] = item.start_date.strftime("%Y-%m-%d %H:%M:%S")
                if item.thc_date_to:
                    data_to_send["thc_date_to"] = item.thc_date_to.strftime("%Y-%m-%d %H:%M:%S")
                item.th_update_loyalty(data_to_send,item.id)
        return res

    def th_update_loyalty(self, data, loyal_sambala_id):
        """
        Hàm cập nhật chương trình loyalty trên hệ thống 2E thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'th_update_loyalty')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/loyalty_program/{loyal_sambala_id}"

        try:
            response = requests.put(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = "✅ Cập nhật chương trình loyalty thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, data, 'th_update_loyalty')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'th_update_loyalty')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'th_update_loyalty')
            return {"status_code": 500, "message": msg}

    def unlink(self):
        """
        Gọi API xóa chường trình khuyến mãi trên hệ thống 2E trước khi xóa trong Odoo.
        """

        # Gọi API xóa trước khi thực hiện unlink()
        for sambala_id in self:
            if sambala_id:  # Chỉ gọi API nếu có sambala_id hợp lệ
                self.delete_update_loyalty(sambala_id.id)
        # Xóa bản ghi trong Odoo
        return super().unlink()

    def delete_update_loyalty(self, loyalty_id):
        """
        Gọi API để xóa chương trình loyalty trên hệ thống 2E.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, loyalty_id, 'delete_update_loyalty')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/loyalty_program/{loyalty_id}"

        try:
            response = requests.delete(url, headers=headers)

            if response.status_code == 200:
                msg = f"✅ Xóa chương trình loyalty (ID: {loyalty_id}) thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, loyalty_id, 'delete_update_loyalty')
            else:
                response_data = response.json()
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, loyalty_id, 'delete_update_loyalty')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, loyalty_id, 'delete_update_loyalty')
            return {"status_code": 500, "message": msg}






<odoo>

    <record id="th_ccs_b2b_lead_view_tree" model="ir.ui.view">
        <field name="name">th_ccs_lead_view_tree</field>
        <field name="model">ccs.lead</field>
        <field name="arch" type="xml">
            <tree string="">
                <field name="name"/>
                <field name="th_partner_id" invisible="1"/>
                <field name="th_last_check" widget="remaining_days" readonly="1"/>
                <field name="th_stage_id"/>
                <field name="th_phone"/>
                <field name="th_email"/>
                <field name="th_status_detail_id"/>
                <field name="th_description"/>
                <field name="th_user_id"/>
                <field name="crm_stage_id" optional="hide"/>
                <field name="th_lead_id" optional="hide"/>
                <field name="crm_user_id" optional="hide"/>
                <field name="th_domain_user_id" optional="hide"/>
                <field name="th_origin_id" optional="hide"/>
                <field name="th_admissions_station_id" optional="hide"/>
                <field name="th_admissions_region_id" optional="hide"/>
                <field name="th_team_id" optional="hide"/>
                <field name="th_opportunity_ready" invisible="1"/>
                <field name="th_opportunity_ready_count"/>
            </tree>
        </field>
    </record>

    <record id="th_ccs_b2b_lead_view_form" model="ir.ui.view">
        <field name="name">th_ccs_lead_view_form</field>
        <field name="model">ccs.lead</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <field name="th_is_won" invisible="1"/>
                    <field name="th_opportunity_ready" invisible="1"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Bị block"
                                attrs="{'invisible': [('th_block_css', '=', False)]}"/>
                    <field name="th_block_css" invisible="1"/>
                    <widget name="web_ribbon" title="Đã tạo cơ hội"
                                attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                    <field name="th_opportunity_ready" invisible="1"/>
                    <div class="oe_title">
                            <h1><field class="text-break" name="name" placeholder="Tên liên hệ" attrs="{'readonly': [('th_is_won', '=', True)]}"/></h1>
                            <h2 class="d-flex gap-2 g-0 align-items-end pb-3">
                                <div>
                                    <label for="th_last_check" class="oe_edit_only pb-1" />
                                    <div class="d-flex align-items-end">
                                        <field name="th_last_check" widget="remaining_days" readonly="1"/>
                                    </div>
                                </div>
                            </h2>
                        </div>
                    <group>
                        <group>
                            <field name="th_partner_id" invisible="1" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_email" widget="email" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
<!--                            <field name="th_phone" widget="phone" attrs="{'readonly': [('th_block_css', '=', True)]}"/>-->
                            <field name="th_phone" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
<!--                            <field name="th_phone2" widget="phone" attrs="{'readonly': [('th_block_css', '=', True)]}"/>-->
                            <field name="th_phone2" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_major_ids" invisible="1" widget="many2many_tags" options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_major_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_origin_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"  domain="[('th_module_ids.name', 'in', ['CRM'])]"/>
                        </group>
                        <group>
                            <field name="th_status_detail_id" required="1" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_customer_attitude_id" options="{'no_create': True, 'no_open': True}"/>
<!--                            <field name="th_domain_stage" invisible="1"/>-->
                            <field name="th_level_up_date" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_domain_user_id" invisible="1" />
<!--                            <field name="th_team_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>-->
                            <field name="th_dividing_ring_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_user_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}" domain="th_domain_user_id"/>
                            <field name="th_lead_id" options="{'no_create': True, 'no_open': True}" readonly="1" attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                            <field name="crm_stage_id" options="{'no_create': True, 'no_open': True}" attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                            <field name="crm_user_id" options="{'no_create': True, 'no_open': True}" attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="description" string="Mô tả">
                            <field name="th_description" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                        </page>
                        <page name="info_partner" string="Thông tin liên hệ">
                            <group>
                                <group string="Thông tin liên hệ">
                                    <label string="Địa chỉ" for="th_street"/>
                                    <div class="o_address_format">
                                        <field name="th_street" placeholder="Địa chỉ..." class="o_address_street"/>
                                        <field name="th_ward_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_district_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_state_id" class="o_address_state" style="width:100%" placeholder="Tỉnh/ Tp" options="{'no_open': True, 'no_quick_create': True}" />
                                        <field name="th_country_id" placeholder="Quốc gia" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>
                                    </div>
                                    <label string="Hộ khẩu thường trú" for="th_street_permanent"/>
                                    <div class="o_address_format">
                                        <field name="th_street_permanent" placeholder="Địa chỉ..." class="o_address_street"/>
                                        <field name="th_ward_permanent_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_district_permanent_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_state_permanent_id" class="o_address_state" style="width:100%" placeholder="Tỉnh/ Tp" options="{'no_open': True, 'no_quick_create': True}"/>
                                        <field name="th_country_permanent_id" placeholder="Quốc gia" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>
                                    </div>
                                </group>
                                <group string="">
                                    <field name="th_title_id" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="th_function"/>
                                    <field name="th_gender"/>
                                    <label for="th_place_of_birth_id" options="{'no_create': True, 'no_open': True}"/>
                                    <div class="o_row">
                                        <field name="th_place_of_birth_id" options='{"no_open": True, "no_create": True}'/>
                                        <label for="th_birthday"/>
                                        <field name="th_birthday"/>
                                    </div>
                                    <label for="th_ethnicity_id"/>
                                    <div class="o_row">
                                        <field name="th_ethnicity_id" options='{"no_open": True, "no_create": True}'/>
                                        <label for="th_religion_id"/>
                                        <field name="th_religion_id" options='{"no_open": True, "no_create": True}'/>
                                    </div>
                                </group>
                            </group>
                        </page>
                        <page name="other_info" string="Thông tin khác">
                            <group string="Thông tin khác">
                                <field name="th_admissions_station_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_admissions_region_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_channel_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_source_group_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_reuse_source" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_crm_b2b_ccs_lead_action" model="ir.actions.act_window">
        <field name="name">Chăm sóc khách hàng</field>
        <field name="res_model">ccs.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create':0, 'edit':0, 'delete':0}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_ccs_b2b_lead_view_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_ccs_b2b_lead_view_form')}),
            ]"/>
    </record>
</odoo>
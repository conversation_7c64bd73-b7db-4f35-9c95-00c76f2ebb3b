<odoo>
    <record id="th_view_order_form" model="ir.ui.view">
        <field name="name">sale.order.form</field>
        <field name="model">sale.order</field>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <form string="Sales Order" class="o_sale_order">
                <header>
                    <field name="authorized_transaction_ids" invisible="1"/>
                    <field name="th_order_vmc_id" invisible="1"/>
                    <button name="payment_action_capture" type="object" data-hotkey="shift+g"
                            string="Capture Transaction" class="oe_highlight"
                            attrs="{'invisible': [('authorized_transaction_ids', '=', [])]}"/>
                    <button name="th_syncss_order_vmc" type="object" string="Kích hoạt lại" class="oe_highlight" attrs="{'invisible': ['|','|',('th_order_vmc_id', '!=', 0),('id', '=', False),('state', '!=', 'sale')]}"/>
                    <button name="payment_action_void" type="object"
                            string="Void Transaction" data-hotkey="shift+v"
                            confirm="Are you sure you want to void the authorized transaction? This action can't be undone."
                            attrs="{'invisible': [('authorized_transaction_ids', '=', [])]}"/>
                    <button id="create_invoice_percentage" name="%(sale.action_view_sale_advance_payment_inv)d" string="Tạo hóa đơn"
                        type="action" context="{'default_advance_payment_method': 'percentage'}" data-hotkey="q"
                       attrs="{'invisible': ['|',('invoice_status', '!=', 'no'), ('state', '!=', 'sale')]}"/>
<!--                    <button name="action_refund_apm" string="Cần hoàn tiền" class="oe_highlight" type="object" attrs="{'invisible': ['|',('invoice_count', '=', 0),('th_status_refund','=', 'all_money')]}" confirm="Bạn có chắc muốn hoàn tiền đơn hàng này"/>-->
                    <field name="th_need_refund" invisible="1"/>
                    <field name="th_payment_invoice_status" invisible="1"/>
                    <button name="action_show_popup_refund_apm" string="Hoàn tiền" class="oe_highlight" type="object"
                        attrs="{'invisible': ['|','|',('th_need_refund', '=', False),('invoice_count', '=', 0),('th_status_refund','=', 'all_money')]}" invisible="context.get('th_is_apm_b2b')"
                        confirm="Bạn có chắc muốn hoàn tiền đơn hàng này"/>
                    <button name="action_confirm" data-hotkey="v"
                        string="Xác nhận" type="object" context="{'validate_analytic': True}"
                        attrs="{'invisible': [('state', 'not in', ['draft'])]}"/>
    <!--                <button name="action_quotation_send" type="object" string="Send PRO-FORMA Invoice" groups="sale.group_proforma_sales" attrs="{'invisible': ['|', ('state', '=', 'draft'), ('invoice_count','&gt;=',1)]}" context="{'proforma': True, 'validate_analytic': True}"/>-->
    <!--                <button name="action_quotation_send" string="Gửi Email" type="object" states="sent,sale" data-hotkey="g" context="{'validate_analytic': True}"/>-->
                    <button name="action_cancel" type="object" string="Hủy"
                            attrs="{'invisible': ['|', ('th_payment_invoice_status', '!=', 'not_paid'), ('state', '=', 'cancel')]}"
                            data-hotkey="z"/>


<!--                    <button name="action_draft" states="cancel" type="object" string="Đặt thành Nháp" data-hotkey="w"/>-->
<!--                    <button name="action_after_sales_care" type="object" string="Chăm sóc sau bán hàng" data-hotkey="z"/>-->
<!--                    <button name="action_draft" states="cancel" type="object" string="Đặt thành Nháp" data-hotkey="w"/>-->
                    <field name="state" widget="statusbar" statusbar_visible="draft,sale"/>
                </header>
                <div
                     class="alert alert-warning mb-0" role="alert"
                     attrs="{'invisible': [('partner_credit_warning', '=', '')]}">
                    <field name="partner_credit_warning"/>
                </div>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_invoice" invisible="context.get('not_open_invoice', False)"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-pencil-square-o"
                            attrs="{'invisible': [('invoice_count', '=', 0)]}"
                            context="{'view_apm': True}">
                            <field name="invoice_count" widget="statinfo" string="Hóa đơn"/>
                        </button>
    <!--                    <button name="action_preview_sale_order" invisible="context.get('not_open_invoice', False)"-->
    <!--                        type="object"-->
    <!--                        class="oe_stat_button"-->
    <!--                        icon="fa-globe icon">-->
    <!--                        <div class="o_field_widget o_stat_info">-->
    <!--                            <span class="o_stat_text">Khách hàng</span>-->
    <!--                            <span class="o_stat_text">Preview</span>-->
    <!--                        </div>-->
    <!--                    </button>-->
                    </div>
                    <field name="th_refund_invoice_apm" string="Đơn hàng cần hoàn tiền" invisible="1"/>
                    <field name="th_refunded_invoice_apm" string="Đơn hàng đã hoàn tiền" invisible="1"/>
                    <field name="th_total_refund_excessive" string="Số tiền hoàn" invisible="1"/>
                    <field name="th_compare_total" invisible="1"/>
                    <field name="th_status_refund" invisible="1"/>
                    <widget name="web_ribbon" title="Đơn hàng cần hoàn tiền"
                            attrs="{'invisible': [('th_refund_invoice_apm', '!=', True)]}"/>
                    <widget name="web_ribbon" title="Đơn hàng đã hoàn 1 phần"
                            attrs="{'invisible': ['|',('th_status_refund', '!=', 'ex_money'),('th_refund_invoice_apm', '=', True)]}" />
                    <widget name="web_ribbon" title="Đơn hàng đã hoàn toàn bộ"
                            attrs="{'invisible': ['|',('th_status_refund','!=', 'all_money'),('th_refund_invoice_apm', '=', True)]}" />
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group name="sale_header">
                        <group name="partner_details">
                            <field name="th_refund_invoice_apm" string="Đơn hàng cần hoàn tiền" invisible="1"/>
                            <field name="th_refunded_invoice_apm" string="Đơn hàng đã hoàn tiền" invisible="1"/>
                            <field name="partner_id" string="Khách hàng" widget="res_partner_many2one" context="{'res_partner_search_mode': 'customer', 'show_address': 1, 'show_vat': True}" options='{"always_reload": True}' readonly="1"/>
                            <field name="th_customer_code" readonly="1" />
                            <field name="th_ownership_unit_id" readonly="1" options="{'no_open': True}"/>
                            <field name="th_customer_code_aum" invisible="1"/>
                            <field name="th_customer_phone" string="Số điện thoại" readonly="1"/>
                            <field name="th_customer_email" string="Email" readonly="1" attrs="{'required': [('th_is_sale_vstep', '=', True)]}"/>
                            <field name="th_customer_birthday" string="Ngày sinh" readonly="1"/>
                            <field name="th_origin_ids" invisible="1"/>
                            <field name="th_no_cancel_order" invisible="1"/>

                            <field name="partner_invoice_id" groups="account.group_delivery_invoice_address" context="{'default_type':'invoice'}" options='{"always_reload": True}'/>
                            <field name="partner_shipping_id" groups="account.group_delivery_invoice_address" context="{'default_type':'delivery'}" options='{"always_reload": True}'/>

                            <field name="th_utm_medium" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                            <field name="th_utm_campaign" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                            <field name="th_utm_source" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                            <field name="th_status" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                            <field name="th_order_vmc_name" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>

                            <field name="th_username" required="1" attrs="{'readonly': [('th_order_vmc_id', '!=', 0)]}"/>
                            <field name="th_is_a_simple_lesson" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="th_is_sale_vstep" invisible="1"/>
                            <field name="th_cohort_id" attrs="{'readonly': [('th_order_vmc_id', '!=', 0)], 'required': [('th_is_sale_vstep', '=', True)],'invisible': [('th_is_sale_vstep', '=', False)]}" options="{'no_open': True}"/>
                        </group>
                        <group name="order_details">
                               <field name="qr_code" widget='image' class="oe_avatar" attrs="{'invisible': ['|',('th_ecommerce_platform', '=', False),('state', '!=', 'sale')]}"/>
                            <field name="th_ecommerce_platform" invisible="1" />
                            <field name="th_apm_id" invisible="1" />
                            <field name="th_origin_id" options="{'no_open': True}" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="th_apm_lead_name"/>
                            <field name="th_apm_source_group_id" string="Nhóm nguồn" options="{'no_open': True}"/>
                            <field name="th_channel_id"  string="Kênh thông tin" options="{'no_open': True}"/>
                            <field name="th_total_received" attrs="{'invisible': [('th_total_received', '=', 0)]}"/>
                            <field name="th_total_received_excessive" attrs="{'invisible': [('th_total_received_excessive', '=', 0)]}"/>

<!--                            <field name="validity_date" attrs="{'invisible': [('state', 'in', ['sale', 'done'])]}"/>-->
                            <div class="o_td_label" groups="base.group_no_one" attrs="{'invisible': [('state', 'in', ['sale', 'done', 'cancel'])]}">
                                <label for="date_order" string="Ngày báo giá"/>
                            </div>
                            <field name="date_order" nolabel="1" groups="base.group_no_one" attrs="{'invisible': [('state', 'in', ['sale', 'done', 'cancel'])]}"/>
                            <div class="o_td_label" attrs="{'invisible': [('state', 'in', ['draft', 'sent'])]}">
                                <label for="date_order" string="Ngày tạo đơn"/>
                            </div>
                            <field name="date_order" attrs="{'invisible': [('state', 'in', ['draft', 'sent'])]}" nolabel="1"/>
                            <field name="show_update_pricelist" invisible="1"/>
                            <field name="th_domain" invisible="1"/>
                            <label for="pricelist_id" groups="product.group_product_pricelist"/>
                            <div groups="product.group_product_pricelist" class="o_row">
                                <field name="pricelist_id" string="Bảng giá" options="{'no_open':True,'no_create': True}" readonly="1"/>

                                <button name="action_update_prices" type="object"
                                    string=" Update Prices"
                                    help="Recompute all prices based on this pricelist"
                                    class="btn-link mb-1 px-0" icon="fa-refresh"
                                    confirm="This will update all unit prices based on the currently set pricelist."
                                    attrs="{'invisible': ['|', ('show_update_pricelist', '=', False), ('state', 'in', ['sale', 'done', 'cancel'])]}"/>
                            </div>
                            <field name="company_id" invisible="1"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="pricelist_id" invisible="1" groups="!product.group_product_pricelist"/>
                            <field name="tax_country_id" invisible="1"/>
                            <field name="payment_term_id" options="{'no_open':True,'no_create': True}"/>
                            <field name="th_status" invisible="1"/>
                            <field name="th_updated" invisible="1"/>
                            <field name="th_account_status" invisible="1"/>
                            <field name="th_introducer_id"/>
                            <field name="ecm_type" attrs="{'readonly': [('state', '!=', 'draft')]}"/>

                        </group>
                    </group>
                    <notebook>
                        <page string="Chi tiết đơn hàng" name="order_lines">

                            <field
                                name="order_line"
                                widget="section_and_note_one2many"
                                mode="tree,kanban"
                                attrs="{'readonly': [('state', 'in', ('done','cancel', 'sale'))]}"
                                context="{'default_th_domain_line': th_domain}"
                                >
                                <form>
                                    <field name="display_type" invisible="1"/>
                                    <!--
                                        We need the sequence field to be here for new lines to be added at the correct position.
                                        TODO: at some point we want to fix this in the framework so that an invisible field is not required.
                                    -->
                                    <field name="sequence" invisible="1"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <group>
                                        <group attrs="{'invisible': [('display_type', '!=', False)]}">
                                            <field name="product_updatable" invisible="1"/>

                                            <field name="product_id"
                                                domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                                context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom, 'company_id': parent.company_id}"
                                                attrs="{

                                                    'readonly': [('product_updatable', '=', False)],
                                                    'required': [('display_type', '=', False)],
                                                }"
                                                force_save="1"
                                                widget="many2one_barcode"
                                                />
                                            <field name="product_type" invisible="1"/>
                                            <field name="invoice_status" invisible="1"/>
                                            <field name="qty_to_invoice" invisible="1"/>
                                            <field name="qty_delivered_method" invisible="1"/>
                                            <field name="price_total" invisible="1"/>
                                            <field name="price_tax" invisible="1"/>
                                            <field name="price_subtotal" invisible="1"/>
                                            <field name="product_uom_readonly" invisible="1"/>
                                            <label for="product_uom_qty"/>
                                            <div class="o_row" name="ordered_qty">
                                                <field
                                                    context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom, 'uom_qty_change':True, 'company_id': parent.company_id}"
                                                    name="product_uom_qty"/>
                                                <field name="product_uom" invisible="1" groups="!uom.group_uom"/>
                                                <field
                                                    name="product_uom"
                                                    force_save="1"
                                                    groups="uom.group_uom"
                                                    class="oe_no_button"
                                                    attrs="{
                                                        'readonly': [('product_uom_readonly', '=', True)],
                                                        'required': [('display_type', '=', False)],
                                                    }"
                                                />
                                            </div>
                                            <label for="qty_delivered" string="Đã giao hàng" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}"/>
                                            <div name="delivered_qty" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}">
                                                <field name="qty_delivered" attrs="{'readonly': [('qty_delivered_method', '!=', 'manual')]}"/>
                                            </div>
                                            <label for="qty_invoiced" string="Đã lập hoá đơn" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}"/>
                                            <div name="invoiced_qty" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}">
                                                <field name="qty_invoiced" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}"/>
                                            </div>
                                            <field name="product_packaging_id" attrs="{'invisible': [('product_id', '=', False)]}" context="{'default_product_id': product_id, 'tree_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}" groups="product.group_stock_packaging" />
                                            <field name="price_unit"/>
                                            <field name="tax_id" widget="many2many_tags" options="{'no_create': True}" context="{'search_view_ref': 'account.account_tax_view_search'}" domain="[('type_tax_use','=','sale'), ('company_id','=',parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                                attrs="{'readonly': [('qty_invoiced', '&gt;', 0)]}"/>
                                            <label for="discount" groups="product.group_discount_per_so_line"/>
                                            <div name="discount" groups="product.group_discount_per_so_line">
                                                <field name="discount" class="oe_inline"/> %
                                            </div>
                                            <!--
                                                We need the sequence field to be here
                                                because we want to be able to overwrite the default sequence value in the JS
                                                in order for new lines to be added at the correct position.
                                                NOTE: at some point we want to fix this in the framework so that an invisible field is not required.
                                            -->
                                            <field name="sequence" invisible="1"/>
                                        </group>
                                        <group attrs="{'invisible': [('display_type', '!=', False)]}">
                                            <label for="customer_lead"/>
                                            <div name="lead">
                                                <field name="customer_lead" class="oe_inline"/> days
                                            </div>
                                            <field name="analytic_distribution" widget="analytic_distribution"
                                               groups="analytic.group_analytic_accounting"
                                               options="{'product_field': 'product_id', 'business_domain': 'sale_order'}"/>
                                        </group>
                                    </group>
                                    <label for="name" string="Description" attrs="{'invisible': [('display_type', '!=', False)]}"/>
                                    <label for="name" string="Section Name (eg. Products, Services)" attrs="{'invisible': [('display_type', '!=', 'line_section')]}"/>
                                    <label for="name" string="Note" attrs="{'invisible': [('display_type', '!=', 'line_note')]}"/>
                                    <field name="name"/>
                                    <div name="invoice_lines" groups="base.group_no_one" attrs="{'invisible': [('display_type', '!=', False)]}">
                                        <label for="invoice_lines"/>
                                        <field name="invoice_lines"/>
                                    </div>
                                    <field name="state" invisible="1"/>
                                    <field name="company_id" invisible="1"/>

                                </form>
                                <tree
                                    string="Sales Order Lines"
                                    editable="bottom"
                                >
                                 <control>
                                        <create name="add_product_control" string="Thêm sản phẩm"/>
                                        <create name="add_section_control" string="Thêm phần" context="{'default_display_type': 'line_section'}"/>
                                        <create name="add_note_control" string="Thêm ghi chú" context="{'default_display_type': 'line_note'}"/>
                                 </control>

                                    <field name="sequence" widget="handle" />
                                    <!-- We do not display the type because we don't want the user to be bothered with that information if he has no section or note. -->
                                    <field name="display_type" invisible="1"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="product_type" invisible="1"/>
                                    <field name="product_updatable" invisible="1"/>
                                    <field name="th_domain_line" invisible="1"/>

                                    <field
                                        name="product_id"
                                        attrs="{
                                            'readonly': [('product_updatable', '=', False)],
                                            'required': [('display_type', '=', False)],
                                        }"
                                        force_save="1"
                                        context="{
                                            'partner_id': parent.partner_id,
                                            'quantity': product_uom_qty,
                                            'pricelist': parent.pricelist_id,
                                            'uom': product_uom,
                                            'company_id': parent.company_id,
                                            'default_lst_price': price_unit,
                                            'default_description_sale': name,
                                        }"
                                        options="{
                                            'no_open': True, 'no_create': True,
                                        }"
                                        domain="th_domain_line"
                                        widget="sol_product_many2one"
                                    />
                                    <field name="name" widget="section_and_note_text" optional="show"/>
                                    <field name="analytic_distribution" widget="analytic_distribution"
                                               optional="hide"
                                               groups="analytic.group_analytic_accounting"
                                               options="{'product_field': 'product_id', 'business_domain': 'sale_order'}"/>
                                    <field
                                        name="product_uom_qty"
                                        decoration-info="(not display_type and invoice_status == 'to invoice')" decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                        context="{
                                            'partner_id': parent.partner_id,
                                            'quantity': product_uom_qty,
                                            'pricelist': parent.pricelist_id,
                                            'uom': product_uom,
                                            'company_id': parent.company_id
                                        }"
                                    />
                                    <field
                                        name="qty_delivered"
                                        decoration-info="(not display_type and invoice_status == 'to invoice')" decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                        string="Đã giao hàng"
                                        attrs="{
                                            'column_invisible': [('parent.state', 'not in', ['sale', 'done'])],
                                            'readonly': [('qty_delivered_method', '!=', 'manual')]
                                        }"
                                        optional="show"
                                    />
                                    <field name="qty_delivered_method" invisible="1"/>
                                    <field  name="th_allocation_value" />
                                    <field  name="price_total" />
                                    <field
                                        name="qty_invoiced"
                                        decoration-info="(not display_type and invoice_status == 'to invoice')" decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                        string="Đã lập hoá đơn"
                                        attrs="{'column_invisible': [('parent.state', 'not in', ['sale', 'done'])]}"
                                        optional="show"
                                    />
                                    <field name="qty_to_invoice" invisible="1"/>
                                    <field name="product_uom_readonly" invisible="1"/>
                                    <field name="product_uom" invisible="1" groups="!uom.group_uom" />
                                    <field
                                        name="product_uom"
                                        force_save="1"
                                        string="UoM"
                                        attrs="{
                                            'readonly': [('product_uom_readonly', '=', True)],
                                            'required': [('display_type', '=', False)],
                                        }"
                                        context="{'company_id': parent.company_id}"
                                        groups="uom.group_uom"
                                        options='{"no_open": True}'
                                        optional="show"
                                    />
                                    <field
                                        name="customer_lead"
                                        optional="hide"
                                        attrs="{'readonly': [('parent.state', 'not in', ['draft', 'sent', 'sale'])]}"
                                    />
                                    <field name="product_packaging_qty" attrs="{'invisible': ['|', ('product_id', '=', False), ('product_packaging_id', '=', False)]}" groups="product.group_stock_packaging" optional="show"/>
                                    <field name="product_packaging_id" attrs="{'invisible': [('product_id', '=', False)]}" context="{'default_product_id': product_id, 'tree_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}" groups="product.group_stock_packaging" optional="show"/>
                                    <field
                                        name="price_unit"
                                        attrs="{'readonly': [('qty_invoiced', '&gt;', 0)]}"
                                    />
                                    <field
                                        name="tax_id" invisible="1"
                                        widget="many2many_tags"
                                        options="{'no_create': True}"
                                        domain="[('type_tax_use','=','sale'),('company_id','=',parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                        context="{'active_test': True}"
                                        attrs="{'readonly': [('qty_invoiced', '&gt;', 0)]}"
                                        optional="show"
                                    />
                                    <field name="discount" string="Chiết khấu" groups="product.group_discount_per_so_line" optional="show" widget="sol_discount"/>
                                    <field name="is_downpayment" invisible="1"/>
                                    <field name="price_subtotal" widget="monetary" groups="account.group_show_line_subtotals_tax_excluded" attrs="{'invisible': [('is_downpayment', '=', True)]}"/>
                                    <field name="price_total" widget="monetary" groups="account.group_show_line_subtotals_tax_included" attrs="{'invisible': [('is_downpayment', '=', True)]}"/>
                                    <field name="state" invisible="1"/>
                                    <field name="invoice_status" invisible="1"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="price_tax" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                </tree>
                                <kanban class="o_kanban_mobile">
                                    <field name="name"/>
                                    <field name="product_id"/>
                                    <field name="product_uom_qty"/>
                                    <field name="product_uom"/>
                                    <field name="price_subtotal"/>
                                    <field name="price_total"/>
                                    <field name="price_tax" invisible="1"/>
                                    <field name="price_total" invisible="1"/>
                                    <field name="price_unit"/>
                                    <field name="display_type"/>
                                    <field name="tax_id" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div t-attf-class="oe_kanban_card oe_kanban_global_click ps-0 pe-0 {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                                <t t-if="!record.display_type.raw_value">
                                                    <div class="row g-0">
                                                        <div class="col-2 pe-3">
                                                            <img t-att-src="kanban_image('product.product', 'image_128', record.product_id.raw_value)" t-att-title="record.product_id.value" t-att-alt="record.product_id.value" style="max-width: 100%;"/>
                                                        </div>
                                                        <div class="col-10">
                                                            <div class="row">
                                                                <div class="col">
                                                                    <strong t-out="record.product_id.value" />
                                                                </div>
                                                                <div class="col-auto">
                                                                    <t t-set="line_price" t-value="record.price_subtotal.value" groups="account.group_show_line_subtotals_tax_excluded"/>
                                                                    <t t-set="line_price" t-value="record.price_total.value" groups="account.group_show_line_subtotals_tax_included"/>
                                                                    <strong class="float-end text-end" t-out="line_price" />
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-12 text-muted">
                                                                    Quantity:
                                                                    <t t-out="record.product_uom_qty.value"/> <t t-out="record.product_uom.value"/>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-12 text-muted">
                                                                    Unit Price:
                                                                    <t t-out="record.price_unit.value"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </t>
                                                <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <t t-out="record.name.value"/>
                                                        </div>
                                                    </div>
                                                </t>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                            <div class="oe_right">
                                <button name="%(sale_loyalty.sale_loyalty_coupon_wizard_action)d" class="btn btn-secondary"
                                    string="MÃ PHIẾU GIẢM GIÁ" type="action" groups="base.group_user" attrs="{'invisible': [('state', '=', 'sale')]}"/>
                                <button name="action_open_reward_wizard" class="btn btn-secondary"
                                    string="kHUYẾN MẠI" type="object" groups="base.group_user" attrs="{'invisible': [('state', '=', 'sale')]}"
                                    help="Cập nhật các dòng khuyến mại hiện tại và chọn phần thưởng mới nếu có."/>
                            </div>
                            <group name="note_group" col="6" class="mt-2 mt-md-0">
                                <group colspan="4">
                                    <field  colspan="2" name="note" nolabel="1" placeholder="Terms and conditions..."/>
                                </group>
                                <group class="oe_subtotal_footer oe_right" colspan="2" name="sale_total">
                                    <field name="amount_total" widget="monetary" readonly="1" style="font-size: 16px; font-weight: bold;"/>
                                    <field name="tax_totals" widget="account-tax-totals-field" nolabel="1" colspan="2" readonly="1" invisible="1"/>
                                </group>
                                <div class="clearfix"/>
                            </group>
                        </page>
                        <page string="Thông tin khác" name="other_information">
                            <group>
                                <group name="sales_person" string="Sales">

                                    <field name="user_id" />
                                    <field name="team_id" kanban_view_ref="%(sales_team.crm_team_view_kanban)s" options="{'no_create': True}"/>
                                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                    <label for="require_signature" string="Xác nhận trực tuyến"/>
                                    <div>
                                        <field name="require_signature" class="oe_inline"/>
                                        <span>Signature</span>
                                        <field name="require_payment" class="oe_inline ms-3"/>
                                        <span>Payment</span>
                                    </div>
                                    <field name="reference" readonly="1" attrs="{'invisible': [('reference', '=', False)]}"/>
                                    <field name="client_order_ref"/>
                                    <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"/>
                                </group>
                                <group name="sale_info" string="Hóa đơn và thanh toán">
                                    <field name="show_update_fpos" invisible="1"/>
                                    <label for="fiscal_position_id"/>
                                    <div class="o_row">
                                        <field name="fiscal_position_id" options="{'no_create': True}"/>
                                        <button name="action_update_taxes" type="object"
                                            string=" Update Taxes"
                                            help="Recompute all taxes based on this fiscal position"
                                            class="btn-link mb-1 px-0" icon="fa-refresh"
                                            confirm="This will update all taxes based on the currently selected fiscal position."
                                            attrs="{'invisible': ['|', ('show_update_fpos', '=', False), ('state', 'in', ['sale', 'done','cancel'])]}"/>
                                    </div>

                                    <field name="partner_invoice_id" invisible="1"/>
    <!--                                <field name="analytic_account_id" context="{'default_partner_id':partner_invoice_id, 'default_name':name}" attrs="{'readonly': [('invoice_count','!=',0),('state','=','sale')]}" groups="analytic.group_analytic_accounting" force_save="1"/>-->
                                    <field name="invoice_status" states="sale,done" groups="base.group_no_one"/>
                                    <!-- test_event_configurator -->
                                    <field name="invoice_status" invisible="1" groups="!base.group_no_one"/>
                                </group>
                            </group>
                            <group>
                                <group name="sale_shipping">
                                    <label for="commitment_date" string="Ngày giao hàng"/>
                                    <div name="commitment_date_div" class="o_row">
                                        <field name="commitment_date"/>
                                        <span name="expected_date_span" class="text-muted">Mong muốn: <field name="expected_date" class="oe_inline" widget="date"/></span>
                                    </div>
                                </group>
                                <group string="Theo dõi" name="sale_reporting">
                                    <group name="technical" colspan="2" class="mb-0">
                                        <field name="origin"/>
                                    </group>
                                    <group name="utm_link" colspan="2" class="mt-0">
<!--                                        <field name="campaign_id" options="{'create_name_field': 'title', 'always_reload': True}"/>-->
                                        <field name="medium_id"/>
                                        <field name="source_id"/>
                                    </group>
                                </group>
                            </group>
                        </page>
                        <page groups="base.group_no_one" string="Chữ kí khách hàng" name="customer_signature" attrs="{'invisible': [('require_signature', '=', False), ('signed_by', '=', False), ('signature', '=', False), ('signed_on', '=', False)]}">
                            <group>
                                <field name="signed_by"/>
                                <field name="signed_on"/>
                                <field name="signature" widget="image"/>
                            </group>
                        </page>
                        <page string="Dữ liệu Getfly" attrs="{'invisible': [('th_data_getfly', '=', False)]}">
                            <field name="th_data_getfly" readonly="1"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
     <record id="view_order_account_form" model="ir.ui.view">
        <field name="name">sale.order.form</field>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <form string="Sales Order" class="o_sale_order">
            <header>
                <field name="authorized_transaction_ids" invisible="1"/>
                <button name="payment_action_capture" type="object" data-hotkey="shift+g"
                        string="Capture Transaction" class="oe_highlight"
                        attrs="{'invisible': [('authorized_transaction_ids', '=', [])]}"/>
                <button name="payment_action_void" type="object"
                        string="Void Transaction" data-hotkey="shift+v"
                        confirm="Are you sure you want to void the authorized transaction? This action can't be undone."
                        attrs="{'invisible': [('authorized_transaction_ids', '=', [])]}"/>
                <button id="create_invoice" name="%(sale.action_view_sale_advance_payment_inv)d" string="Tạo hóa đơn"
                    type="action" class="btn-primary" data-hotkey="q"
                    attrs="{'invisible': [('invoice_status', '!=', 'to invoice')]}" invisible="context.get('refund_apm')"/>
                <button id="create_invoice_percentage" name="%(sale.action_view_sale_advance_payment_inv)d" string="Tạo hóa đơn"
                    type="action" context="{'default_advance_payment_method': 'percentage'}" data-hotkey="q"
                    attrs="{'invisible': ['|',('invoice_status', '!=', 'no'), ('state', '!=', 'sale')]}"/>
                <button name="action_quotation_send" string="Send by Email" type="object" states="draft" class="btn-primary" data-hotkey="g" context="{'validate_analytic': True}"/>
                <button name="action_quotation_send" type="object" string="Gửi hóa đơn PRO-FORMA"
                    groups="sale.group_proforma_sales" class="btn-primary"
                    attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('invoice_count','&gt;=',1)]}" context="{'proforma': True, 'validate_analytic': True}"/>
                <button name="action_confirm" id="action_confirm" data-hotkey="v"
                    string="Xác nhận" class="btn-primary" type="object" context="{'validate_analytic': True}"
                    attrs="{'invisible': [('state', 'not in', ['sent'])]}"/>
                <button name="action_confirm" data-hotkey="v"
                    string="Xác nhận" type="object" context="{'validate_analytic': True}"
                    attrs="{'invisible': [('state', 'not in', ['draft'])]}"/>
<!--                <button name="action_refund_apm" string="Cần hoàn tiền" class="oe_highlight" type="object" attrs="{'invisible': ['|',('invoice_count', '=', 0),('th_status_refund','=', 'all_money')]}" confirm="Bạn có chắc muốn hoàn tiền đơn hàng này"/>-->
                <button name="action_show_popup_refund_apm" string="Hoàn tiền" class="oe_highlight" type="object"
                        attrs="{'invisible': ['|',('invoice_count', '=', 0),('th_status_refund','=', 'all_money')]}"
                        confirm="Bạn có chắc muốn hoàn tiền đơn hàng này"/>
                <button name="action_quotation_send" type="object" string="Gửi hóa đơn PRO-FORMA" groups="sale.group_proforma_sales" attrs="{'invisible': ['|', ('state', '=', 'draft'), ('invoice_count','&gt;=',1)]}" context="{'proforma': True, 'validate_analytic': True}"/>
                <button name="action_quotation_send" string="Gửi Email" type="object" states="sent,sale" data-hotkey="g" context="{'validate_analytic': True}"/>
                <button name="action_cancel" type="object" string="Hủy" attrs="{'invisible': [('th_no_cancel_order', '=', True)]}" data-hotkey="z"/>
                <button name="action_draft" states="cancel" type="object" string="Set to Quotation" data-hotkey="w"/>
                <field name="state" widget="statusbar" statusbar_visible="draft,sale"/>
            </header>
            <div
                 class="alert alert-warning mb-0" role="alert"
                 attrs="{'invisible': [('partner_credit_warning', '=', '')]}">
                <field name="partner_credit_warning"/>
            </div>
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button name="action_view_invoice" invisible="context.get('not_open_invoice', False)"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-pencil-square-o"
                            attrs="{'invisible': [('invoice_count', '=', 0)]}"
                            >
                            <field name="invoice_count" widget="statinfo" string="Hóa đơn"/>
                        </button>
                    <button name="action_preview_sale_order"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-globe icon">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Customer</span>
                            <span class="o_stat_text">Preview</span>
                        </div>
                    </button>
                </div>
                <field name="th_refund_invoice_apm" string="Đơn hàng cần hoàn tiền" invisible="1"/>
                <field name="th_refunded_invoice_apm" string="Đơn hàng đã hoàn tiền" invisible="1"/>
                <field name="th_total_refund_excessive" string="Số tiền hoàn" invisible="1"/>
                    <field name="th_compare_total" invisible="1"/>
                    <field name="th_status_refund" invisible="1"/>
                    <widget name="web_ribbon" title="Đơn hàng cần hoàn tiền"
                            attrs="{'invisible': [('th_refund_invoice_apm', '!=', True)]}"/>
                    <widget name="web_ribbon" title="Đơn hàng đã hoàn 1 phần"
                            attrs="{'invisible': ['|',('th_status_refund', '!=', 'ex_money'),('th_refund_invoice_apm', '=', True)]}" />
                    <widget name="web_ribbon" title="Đơn hàng đã hoàn toàn bộ"
                            attrs="{'invisible': ['|',('th_status_refund','!=', 'all_money'),('th_refund_invoice_apm', '=', True)]}" />
                <div class="oe_title">
                    <h1>
                        <field name="name" readonly="1"/>
                    </h1>
                </div>
                <group name="sale_header">
                    <group name="partner_details">
                        <field name="partner_id" widget="res_partner_many2one" context="{'res_partner_search_mode': 'customer', 'show_address': 1, 'show_vat': True}" options='{"always_reload": True}'/>
                        <field name="partner_invoice_id" groups="account.group_delivery_invoice_address" context="{'default_type':'invoice'}" options='{"always_reload": True}'/>
                        <field name="partner_shipping_id" groups="account.group_delivery_invoice_address" context="{'default_type':'delivery'}" options='{"always_reload": True}'/>
                        <field name="th_customer_phone" string="Số điện thoại" readonly="1"/>
                        <field name="th_customer_email" string="Email" readonly="1"/>
                        <field name="th_customer_birthday" string="Ngày sinh" readonly="1"/>
                        <field name="th_customer_code" readonly="1"/>
                        <field name="th_customer_code_aum" invisible="1"/>
                        <field name="th_no_cancel_order" invisible="1"/>
                        <field name="th_utm_medium" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                        <field name="th_utm_campaign" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                        <field name="th_utm_source" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                        <field name="th_status" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                        <field name="th_order_vmc_name" readonly="1" attrs="{'invisible': [('th_order_vmc_name', '=', False)]}"/>
                    </group>
                    <group name="order_details">
                            <field name="th_apm_source_group_id" string="Nhóm nguồn" options="{'no_open': True}"/>/>
                            <field name="th_channel_id"  string="Kênh thông tin" options="{'no_open': True}"/>/>
                            <field name="th_total_received" attrs="{'invisible': [('th_total_received', '=', 0)]}"/>
                            <field name="th_total_received_excessive" attrs="{'invisible': [('th_total_received_excessive', '=', 0)]}"/>

<!--                            <field name="validity_date" attrs="{'invisible': [('state', 'in', ['sale', 'done'])]}"/>-->
                            <div class="o_td_label" groups="base.group_no_one" attrs="{'invisible': [('state', 'in', ['sale', 'done', 'cancel'])]}">
                                <label for="date_order" string="Ngày báo giá"/>
                            </div>
                            <field name="date_order" nolabel="1" groups="base.group_no_one" attrs="{'invisible': [('state', 'in', ['sale', 'done', 'cancel'])]}"/>
                            <div class="o_td_label" attrs="{'invisible': [('state', 'in', ['draft', 'sent'])]}">
                                <label for="date_order" string="Ngày tạo đơn"/>
                            </div>
                            <field name="date_order" attrs="{'invisible': [('state', 'in', ['draft', 'sent'])]}" nolabel="1"/>
                            <field name="show_update_pricelist" invisible="1"/>
                            <field name="th_domain" invisible="1"/>
                            <label for="pricelist_id" groups="product.group_product_pricelist"/>
                            <div groups="product.group_product_pricelist" class="o_row">
                                <field name="pricelist_id" string="Bảng giá" options="{'no_open':True,'no_create': True}" readonly="1"/>

                                <button name="action_update_prices" type="object"
                                    string=" Update Prices"
                                    help="Recompute all prices based on this pricelist"
                                    class="btn-link mb-1 px-0" icon="fa-refresh"
                                    confirm="This will update all unit prices based on the currently set pricelist."
                                    attrs="{'invisible': ['|', ('show_update_pricelist', '=', False), ('state', 'in', ['sale', 'done', 'cancel'])]}"/>
                            </div>
                            <field name="company_id" invisible="1"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="pricelist_id" invisible="1" groups="!product.group_product_pricelist"/>
                            <field name="tax_country_id" invisible="1"/>
                            <field name="payment_term_id" options="{'no_open':True,'no_create': True}"/>
                            <field name="th_introducer_id"/>
                            <field name="ecm_type" attrs="{'readonly': [('state', '!=', 'draft')]}"/>


                        </group>
                </group>
                <notebook>
                    <page string="Chi tiết đơn hàng" name="order_lines">
                        <field
                            name="order_line"
                            widget="section_and_note_one2many"
                            mode="tree,kanban"
                            attrs="{'readonly': [('state', 'in', ('done','cancel', 'sale'))]}"
                        >
                            <form>
                                <field name="display_type" invisible="1"/>
                                <!--
                                    We need the sequence field to be here for new lines to be added at the correct position.
                                    TODO: at some point we want to fix this in the framework so that an invisible field is not required.
                                -->
                                <field name="sequence" invisible="1"/>
                                <field name="product_uom_category_id" invisible="1"/>
                                <group>
                                    <group attrs="{'invisible': [('display_type', '!=', False)]}">
                                        <field name="product_updatable" invisible="1"/>
                                        <field name="product_id"
                                            domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                            context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom, 'company_id': parent.company_id}"
                                            attrs="{
                                                'readonly': [('product_updatable', '=', False)],
                                                'required': [('display_type', '=', False)],
                                            }"
                                            force_save="1"
                                            widget="many2one_barcode"
                                            />
                                        <field name="product_type" invisible="1"/>
                                        <field name="invoice_status" invisible="1"/>
                                        <field name="qty_to_invoice" invisible="1"/>
                                        <field name="qty_delivered_method" invisible="1"/>
                                        <field name="price_total" invisible="1"/>
                                        <field name="price_tax" invisible="1"/>
                                        <field name="price_subtotal" invisible="1"/>
                                        <field name="product_uom_readonly" invisible="1"/>
                                        <label for="product_uom_qty"/>
                                        <div class="o_row" name="ordered_qty">
                                            <field
                                                context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom, 'uom_qty_change':True, 'company_id': parent.company_id}"
                                                name="product_uom_qty"/>
                                            <field name="product_uom" invisible="1" groups="!uom.group_uom"/>
                                            <field
                                                name="product_uom"
                                                force_save="1"
                                                groups="uom.group_uom"
                                                class="oe_no_button"
                                                attrs="{
                                                    'readonly': [('product_uom_readonly', '=', True)],
                                                    'required': [('display_type', '=', False)],
                                                }"
                                            />
                                        </div>
                                        <label for="qty_delivered" string="Delivered" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}"/>
                                        <div name="delivered_qty" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}">
                                            <field name="qty_delivered" attrs="{'readonly': [('qty_delivered_method', '!=', 'manual')]}"/>
                                        </div>
                                        <label for="qty_invoiced" string="Hóa đơn" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}"/>
                                        <div name="invoiced_qty" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}">
                                            <field name="qty_invoiced" attrs="{'invisible': [('parent.state', 'not in', ['sale', 'done'])]}"/>
                                        </div>
                                        <field name="product_packaging_id" attrs="{'invisible': [('product_id', '=', False)]}" context="{'default_product_id': product_id, 'tree_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}" groups="product.group_stock_packaging" />
                                        <field name="price_unit"/>
                                        <field name="tax_id" widget="many2many_tags" options="{'no_create': True}" context="{'search_view_ref': 'account.account_tax_view_search'}" domain="[('type_tax_use','=','sale'), ('company_id','=',parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                            attrs="{'readonly': [('qty_invoiced', '&gt;', 0)]}"/>
                                        <label for="discount" groups="product.group_discount_per_so_line"/>
                                        <div name="discount" groups="product.group_discount_per_so_line">
                                            <field name="discount" class="oe_inline"/> %
                                        </div>
                                        <!--
                                            We need the sequence field to be here
                                            because we want to be able to overwrite the default sequence value in the JS
                                            in order for new lines to be added at the correct position.
                                            NOTE: at some point we want to fix this in the framework so that an invisible field is not required.
                                        -->
                                        <field name="sequence" invisible="1"/>
                                    </group>
                                    <group attrs="{'invisible': [('display_type', '!=', False)]}">
                                        <label for="customer_lead"/>
                                        <div name="lead">
                                            <field name="customer_lead" class="oe_inline"/> days
                                        </div>
                                        <field name="analytic_distribution" widget="analytic_distribution"
                                           groups="analytic.group_analytic_accounting"
                                           options="{'product_field': 'product_id', 'business_domain': 'sale_order'}"/>
                                    </group>
                                </group>
                                <label for="name" string="Description" attrs="{'invisible': [('display_type', '!=', False)]}"/>
                                <label for="name" string="Section Name (eg. Products, Services)" attrs="{'invisible': [('display_type', '!=', 'line_section')]}"/>
                                <label for="name" string="Note" attrs="{'invisible': [('display_type', '!=', 'line_note')]}"/>
                                <field name="name"/>
                                <div name="invoice_lines" groups="base.group_no_one" attrs="{'invisible': [('display_type', '!=', False)]}">
                                    <label for="invoice_lines"/>
                                    <field name="invoice_lines"/>
                                </div>
                                <field name="state" invisible="1"/>
                                <field name="company_id" invisible="1"/>
                            </form>
                            <tree
                                string="Sales Order Lines"
                                editable="bottom"
                            >
                                <control>
                                    <create name="add_product_control" string="Add a product"/>
                                    <create name="add_section_control" string="Add a section" context="{'default_display_type': 'line_section'}"/>
                                    <create name="add_note_control" string="Add a note" context="{'default_display_type': 'line_note'}"/>
                                </control>

                                <field name="sequence" widget="handle" />
                                <!-- We do not display the type because we don't want the user to be bothered with that information if he has no section or note. -->
                                <field name="display_type" invisible="1"/>
                                <field name="product_uom_category_id" invisible="1"/>
                                <field name="product_type" invisible="1"/>
                                <field name="product_updatable" invisible="1"/>
                                <field
                                    name="product_id"
                                    attrs="{
                                        'readonly': [('product_updatable', '=', False)],
                                        'required': [('display_type', '=', False)],
                                    }"
                                    force_save="1"
                                    context="{
                                        'partner_id': parent.partner_id,
                                        'quantity': product_uom_qty,
                                        'pricelist': parent.pricelist_id,
                                        'uom':product_uom,
                                        'company_id': parent.company_id,
                                        'default_lst_price': price_unit,
                                        'default_description_sale': name
                                    }"
                                    options="{
                                        'no_open': True,
                                    }"
                                    domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                    widget="sol_product_many2one"
                                />
                                <field name="product_template_id"
                                    string="Product"
                                    invisible="1"
                                    attrs="{
                                        'readonly': [('product_updatable', '=', False)],
                                        'required': [('display_type', '=', False)],
                                    }"
                                    context="{
                                        'partner_id': parent.partner_id,
                                        'quantity': product_uom_qty,
                                        'pricelist': parent.pricelist_id,
                                        'uom':product_uom,
                                        'company_id': parent.company_id,
                                        'default_list_price': price_unit,
                                        'default_description_sale': name
                                    }"
                                    options="{
                                        'no_open': True,
                                    }"
                                    domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                    widget="sol_product_many2one"/>
                                <field name="name" widget="section_and_note_text" optional="show"/>
                                <field name="analytic_distribution" widget="analytic_distribution"
                                           optional="hide"
                                           groups="analytic.group_analytic_accounting"
                                           options="{'product_field': 'product_id', 'business_domain': 'sale_order'}"/>
                                <field
                                    name="product_uom_qty"
                                    decoration-info="(not display_type and invoice_status == 'to invoice')" decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                    context="{
                                        'partner_id': parent.partner_id,
                                        'quantity': product_uom_qty,
                                        'pricelist': parent.pricelist_id,
                                        'uom': product_uom,
                                        'company_id': parent.company_id
                                    }"
                                />
                                <field
                                    name="qty_delivered"
                                    decoration-info="(not display_type and invoice_status == 'to invoice')" decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                    string="Đã giao hàng"
                                    attrs="{
                                        'column_invisible': [('parent.state', 'not in', ['sale', 'done'])],
                                        'readonly': [('qty_delivered_method', '!=', 'manual')]
                                    }"
                                    optional="show"
                                />
                                <field name="qty_delivered_method" invisible="1"/>
                                <field
                                    name="qty_invoiced"
                                    decoration-info="(not display_type and invoice_status == 'to invoice')" decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                    string="Hóa đơn"
                                    attrs="{'column_invisible': [('parent.state', 'not in', ['sale', 'done'])]}"
                                    optional="show"
                                />
                                <field name="qty_to_invoice" invisible="1"/>
                                <field name="product_uom_readonly" invisible="1"/>
                                <field name="product_uom" invisible="1" groups="!uom.group_uom"/>
                                <field
                                    name="product_uom"
                                    force_save="1"
                                    string="UoM"
                                    attrs="{
                                        'readonly': [('product_uom_readonly', '=', True)],
                                        'required': [('display_type', '=', False)],
                                    }"
                                    context="{'company_id': parent.company_id}"
                                    groups="uom.group_uom"
                                    options='{"no_open": True}'
                                    optional="show"
                                />
                                <field
                                    name="customer_lead"
                                    optional="hide"
                                    attrs="{'readonly': [('parent.state', 'not in', ['draft', 'sent', 'sale'])]}"
                                />
                                <field name="product_packaging_qty" attrs="{'invisible': ['|', ('product_id', '=', False), ('product_packaging_id', '=', False)]}" groups="product.group_stock_packaging" optional="show"/>
                                <field name="product_packaging_id" attrs="{'invisible': [('product_id', '=', False)]}" context="{'default_product_id': product_id, 'tree_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}" groups="product.group_stock_packaging" optional="show"/>
                                <field
                                    name="price_unit"
                                    attrs="{'readonly': [('qty_invoiced', '&gt;', 0)]}"
                                />
                                <field
                                    name="tax_id" invisible="1"
                                    widget="many2many_tags"
                                    options="{'no_create': True}"
                                    domain="[('type_tax_use','=','sale'),('company_id','=',parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                    context="{'active_test': True}"
                                    attrs="{'readonly': [('qty_invoiced', '&gt;', 0)]}"
                                    optional="show"
                                />
                                <field name="discount" string="Chiết khấu" groups="product.group_discount_per_so_line" optional="show" widget="sol_discount"/>
                                <field name="is_downpayment" invisible="1"/>
                                <field name="price_subtotal" widget="monetary" groups="account.group_show_line_subtotals_tax_excluded" attrs="{'invisible': [('is_downpayment', '=', True)]}"/>
                                <field name="price_total" widget="monetary" groups="account.group_show_line_subtotals_tax_included" attrs="{'invisible': [('is_downpayment', '=', True)]}"/>
                                <field name="state" invisible="1"/>
                                <field name="invoice_status" invisible="1"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="price_tax" invisible="1"/>
                                <field name="company_id" invisible="1"/>
                            </tree>
                            <kanban class="o_kanban_mobile">
                                <field name="name"/>
                                <field name="product_id"/>
                                <field name="product_uom_qty"/>
                                <field name="product_uom"/>
                                <field name="price_subtotal"/>
                                <field name="price_total"/>
                                <field name="price_tax" invisible="1"/>
                                <field name="price_total" invisible="1"/>
                                <field name="price_unit"/>
                                <field name="display_type"/>
                                <field name="tax_id" invisible="1"/>
                                <field name="company_id" invisible="1"/>
                                <templates>
                                    <t t-name="kanban-box">
                                        <div t-attf-class="oe_kanban_card oe_kanban_global_click ps-0 pe-0 {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                            <t t-if="!record.display_type.raw_value">
                                                <div class="row g-0">
                                                    <div class="col-2 pe-3">
                                                        <img t-att-src="kanban_image('product.product', 'image_128', record.product_id.raw_value)" t-att-title="record.product_id.value" t-att-alt="record.product_id.value" style="max-width: 100%;"/>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="row">
                                                            <div class="col">
                                                                <strong t-out="record.product_id.value" />
                                                            </div>
                                                            <div class="col-auto">
                                                                <t t-set="line_price" t-value="record.price_subtotal.value" groups="account.group_show_line_subtotals_tax_excluded"/>
                                                                <t t-set="line_price" t-value="record.price_total.value" groups="account.group_show_line_subtotals_tax_included"/>
                                                                <strong class="float-end text-end" t-out="line_price" />
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-12 text-muted">
                                                                Quantity:
                                                                <t t-out="record.product_uom_qty.value"/> <t t-out="record.product_uom.value"/>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-12 text-muted">
                                                                Unit Price:
                                                                <t t-out="record.price_unit.value"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </t>
                                            <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <t t-out="record.name.value"/>
                                                    </div>
                                                </div>
                                            </t>
                                        </div>
                                    </t>
                                </templates>
                            </kanban>
                        </field>
                        <group name="note_group" col="6" class="mt-2 mt-md-0">
                            <group colspan="4">
                                <field  colspan="2" name="note" nolabel="1" placeholder="Terms and conditions..."/>
                            </group>
                            <group class="oe_subtotal_footer oe_right" colspan="2" name="sale_total">
                                <field name="amount_total" widget="monetary" readonly="1" style="font-size: 16px; font-weight: bold;"/>
                                <field name="tax_totals" invisible="1" widget="account-tax-totals-field" nolabel="1" colspan="2" readonly="1"/>
                            </group>
                            <div class="clearfix"/>
                        </group>
                    </page>
                    <page string="Thông tin khác" name="other_information">
                        <group>
                            <group name="sales_person" string="Sales">
                                <field name="user_id" />
                                <field name="team_id" kanban_view_ref="%(sales_team.crm_team_view_kanban)s" options="{'no_create': True}"/>
                                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                <label for="require_signature" string="Xác nhận trực tuyến"/>
                                <div>
                                    <field name="require_signature" class="oe_inline"/>
                                    <span>Signature</span>
                                    <field name="require_payment" class="oe_inline ms-3"/>
                                    <span>Payment</span>
                                </div>
                                <field name="reference" readonly="1" attrs="{'invisible': [('reference', '=', False)]}"/>
                                <field name="client_order_ref"/>
                                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"/>
                            </group>
                            <group name="sale_info" string="Hóa đơn và thanh toán">
                                <field name="show_update_fpos" invisible="1"/>
                                <label for="fiscal_position_id"/>
                                <div class="o_row">
                                    <field name="fiscal_position_id" options="{'no_create': True}"/>
                                    <button name="action_update_taxes" type="object"
                                        string=" Update Taxes"
                                        help="Recompute all taxes based on this fiscal position"
                                        class="btn-link mb-1 px-0" icon="fa-refresh"
                                        confirm="This will update all taxes based on the currently selected fiscal position."
                                        attrs="{'invisible': ['|', ('show_update_fpos', '=', False), ('state', 'in', ['sale', 'done','cancel'])]}"/>
                                </div>
                                <field name="partner_invoice_id" groups="!account.group_delivery_invoice_address" invisible="1"/>
                                <field name="analytic_account_id" context="{'default_partner_id':partner_invoice_id, 'default_name':name}" attrs="{'readonly': [('invoice_count','!=',0),('state','=','sale')]}" groups="analytic.group_analytic_accounting" force_save="1"/>
                                <field name="invoice_status" states="sale,done" groups="base.group_no_one"/>
                                <!-- test_event_configurator -->
                                <field name="invoice_status" invisible="1" groups="!base.group_no_one"/>
                            </group>
                        </group>
                        <group>
                            <group name="sale_shipping">
                                <label for="commitment_date" string="Ngày giao hàng"/>
                                <div name="commitment_date_div" class="o_row">
                                    <field name="commitment_date"/>
                                    <span name="expected_date_span" class="text-muted">Expected: <field name="expected_date" class="oe_inline" widget="date"/></span>
                                </div>
                            </group>
                            <group string="Theo dõi" name="sale_reporting">
                                <group name="technical" colspan="2" class="mb-0">
                                    <field name="origin"/>
                                </group>
                                <group name="utm_link" colspan="2" class="mt-0">
                                    <field name="campaign_id" options="{'create_name_field': 'title', 'always_reload': True}"/>
                                    <field name="medium_id"/>
                                    <field name="source_id"/>
                                </group>
                            </group>
                        </group>
                    </page>
                    <page groups="base.group_no_one" string="Chữ ký của khách hàng" name="customer_signature" attrs="{'invisible': [('require_signature', '=', False), ('signed_by', '=', False), ('signature', '=', False), ('signed_on', '=', False)]}">
                        <group>
                            <field name="signed_by"/>
                            <field name="signed_on"/>
                            <field name="signature" widget="image"/>
                        </group>
                    </page>
                </notebook>
            </sheet>
            <div class="oe_chatter">
                <field name="message_follower_ids"/>
                <field name="activity_ids"/>
                <field name="message_ids"/>
            </div>
            </form>
        </field>
    </record>
    <record id="view_order_tree" model="ir.ui.view">
        <field name="name">sale.order.tree</field>
        <field name="model">sale.order</field>
        <field name="priority">2</field>
        <field name="arch" type="xml">
            <tree string="Sales Orders" sample="1"
                decoration-info="invoice_status == 'to invoice'"
                decoration-muted="state == 'cancel'">
<!--                <header>-->
<!--                    <button name="action_refund_apm" string="Cần hoàn tiền" class="oe_highlight" type="object"  attrs="{'invisible': [('invoice_count', '=', 0)]}" confirm="Bạn có chắc muốn hoàn tiền đơn hàng này"/>-->
<!--                </header>-->
                <field name="message_needaction" invisible="1"/>
                <field name="name" string="Mã" readonly="1" decoration-bf="1"/>
                <field name="date_order" string="Ngày đặt hàng" widget="date" optional="show" invisible="1"/>
                <field name="commitment_date" optional="hide"/>
                <field name="expected_date" optional="hide" string="Ngày tạo đơn"/>
                <field name="partner_id" readonly="1" string="Khách hàng"/>
                <field name="user_id" optional="show" />
                <field name="activity_ids" widget="list_activity" optional="show"/>
                <field name="team_id" optional="hide"/>
                <field name="invoice_count" optional="show"/>
<!--                <field name="th_refund_invoice_apm" optional="show" string="Cần hoàn tiền"/>-->
<!--                <field name="th_refunded_invoice_apm" optional="show" string="Đã hoàn tiền"/>-->
                <field name="company_id" groups="base.group_multi_company" optional="show" readonly="1"/>
                <field name="amount_untaxed" sum="Total Tax Excluded" widget="monetary" optional="hide"/>
                <field name="amount_tax" sum="Tax Total" widget="monetary" optional="hide"/>
                <field name="amount_total" sum="Total Tax Included" widget="monetary" decoration-bf="1" optional="show"/>
                <field name="currency_id" invisible="1"/>
                <field name="invoice_status"
                    decoration-success="invoice_status == 'invoiced'"
                    decoration-info="invoice_status == 'to invoice'"
                    decoration-warning="invoice_status == 'upselling'"
                    widget="badge" optional="show" invisible="1"/>
                <field name="tag_ids" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="state" invisible="0"/>
                <field name="th_customer_phone" optional="hide"/>
                <field name="th_customer_email" optional="hide"/>
                <field name="th_customer_birthday" optional="hide"/>
                <field name="th_apm_source_group_id" optional="hide"/>
                <field name="th_channel_id" optional="hide"/>
                <button class="oe_highlight" type="object" name="action_order_details" string="Chi tiết đơn hàng" invisible="context.get('invisible_button', False)"/>
            </tree>
        </field>
    </record>
    <record id="view_order_account_tree" model="ir.ui.view">
        <field name="name">sale.order.tree</field>
        <field name="model">sale.order</field>
        <field name="priority">2</field>
        <field name="arch" type="xml">
            <tree string="Sales Orders" sample="1"
                decoration-info="invoice_status == 'to invoice'"
                decoration-muted="state == 'cancel'" js_class="apm_lead_list">
                <header>
                    <button string="Tạo hóa đơn" type="object"
                            name="th_create_a_invoice" class="btn-primary" invisible="not context.get('no_invoice')"/>
                </header>
                <field name="message_needaction" invisible="1"/>
                <field name="name" string="Mã" readonly="1" decoration-bf="1"/>
                <field name="date_order" string="Ngày đặt hàng" widget="date" optional="show" invisible="1"/>
                <field name="commitment_date" optional="hide"/>
                <field name="expected_date" optional="hide" string="Ngày tạo đơn"/>
                <field name="partner_id" readonly="1" string="Khách hàng"/>
                <field name="user_id" optional="show" />
                <field name="activity_ids" widget="list_activity" optional="show"/>
                <field name="team_id" optional="hide"/>
                <field name="th_paid_order_compute" invisible="1"/>
                <field name="th_no_cancel_order" invisible="1"/>
                <field name="company_id" groups="base.group_multi_company" optional="show" readonly="1"/>
                <field name="amount_untaxed" sum="Total Tax Excluded" widget="monetary" optional="hide"/>
                <field name="th_prepaid_amount" widget="monetary"/>
                <field name="amount_tax" sum="Tax Total" widget="monetary" optional="hide"/>
                <field name="amount_total" sum="Total Tax Included" widget="monetary" decoration-bf="1" optional="show"/>
                <field name="currency_id" invisible="1"/>
                <field name="invoice_status"
                    decoration-success="invoice_status == 'invoiced'"
                    decoration-info="invoice_status == 'to invoice'"
                    decoration-warning="invoice_status == 'upselling'"
                    widget="badge" optional="show" invisible="1"/>
                <field name="tag_ids" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="state" invisible="0"/>
<!--                <button class="oe_highlight" type="object" name="action_order_details" string="Chi tiết đơn hàng" invisible="context.get('invisible_button', False)"/>-->
            </tree>
        </field>
    </record>
    <record id="th_view_orders_search" model="ir.ui.view">
        <field name="name">th.view.order.search</field>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="partner_id" filter_domain="[('partner_id.phone', '=', self)]" string="Số điện thoại"/>
                <field name="th_customer_email" string="Email"/>
                <field name="th_origin_id" string="Dòng sản phẩm"/>
                <field name="th_customer_birthday" string="Ngày sinh"/>
<!--                <field name="name" string="Order" filter_domain="['|', '|', ('name', 'ilike', self), ('client_order_ref', 'ilike', self), ('partner_id', 'child_of', self)]" />-->
                <field name="partner_id" operator="child_of" string="Khách hàng"/>
                <filter name="th_status_refund_searchpanel" string="trạng thái hoàn tiền không được thiết lập" domain="[('th_status_refund_search', '=', False)]"/>
                <filter name="th_is_a_simple_lesson" string="Đơn hàng Học thử"
                            domain="[('th_is_a_simple_lesson', '=', True)]"/>
                <field name="user_id"/>
                <field name="team_id" string="Đội chăm sóc"/>
                <field name="order_line" string="Sản phẩm" filter_domain="[('order_line.product_id', 'ilike', self)]"/>
                <field name="th_apm_source_group_id" filter_domain="[('th_apm_source_group_id', 'ilike', self)]"/>
                <field name="th_channel_id" filter_domain="[('th_channel_id', 'ilike', self)]"/>
                <field name="analytic_account_id" groups="analytic.group_analytic_accounting"/>
                <filter name="state" string="Trạng thái" context="{'group_by':'state'}"/>
                <searchpanel>
                    <field name="state" enable_counters="1"/>
                    <field name="th_payment_invoice_status" enable_counters="1"/>
                    <field name="th_status_refund_search" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="th_view_orders_account_search" model="ir.ui.view">
        <field name="name">th.view.order.search</field>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <search>
                <field name="partner_id" filter_domain="[('partner_id.phone', '=', self)]" string="Số điện thoại"/>
                <field name="th_customer_email" string="Email"/>
<!--                <field name="name" string="Order" filter_domain="['|', '|', ('name', 'ilike', self), ('client_order_ref', 'ilike', self), ('partner_id', 'child_of', self)]" />-->
                <field name="partner_id" operator="child_of" string="Khách hàng"/>
                <field name="user_id"/>
                <field name="team_id" string="Đội chăm sóc"/>
                <field name="order_line" string="Sản phẩm" filter_domain="[('order_line.product_id', 'ilike', self)]"/>
                <field name="th_apm_source_group_id" filter_domain="[('th_apm_source_group_id', 'ilike', self)]"/>
                <field name="th_channel_id" filter_domain="[('th_channel_id', 'ilike', self)]"/>
                <field name="analytic_account_id" groups="analytic.group_analytic_accounting"/>
                <filter name="state" string="Trạng thái" context="{'group_by':'state'}"/>

                <group>
                    <filter name="apm_order" string="Đơn hàng APM"
                            domain="[('th_apm_id', '!=', False)]"/>
                    <filter name="crm_order" string="Đơn hàng CRM"
                            domain="[('th_sale_order', '=', 'crm')]"/>
                    <filter name="th_apm_need_payment" string="Đơn hàng cần thanh toán"
                            domain="[('th_apm_id', '!=', False),('th_paid_order', '=', False)]" invisible="not context.get('invisible_button')"/>
                    <filter name="th_apm_account_paid" string="Đơn hàng đã thanh toán"
                            domain="[('th_apm_id', '!=', False),('th_paid_order', '!=', False)]" invisible="not context.get('invisible_button')"/>
                    <filter name="th_apm_refunded_invoice" string="Đơn hàng đã hoàn tiền APM"
                            domain="[('th_apm_id', '!=', False),('th_refunded_invoice_apm', '!=', False)]" invisible="not context.get('refund_apm')"/>
                    <filter name="th_apm_refund_invoice" string="Đơn hàng cần hoàn tiền APM"
                            domain="[('th_apm_id', '!=', False),('th_refund_invoice_apm', '!=', False)]" invisible="not context.get('refund_apm')"/>
<!--                    <filter name="srm_order" string="Đơn hàng SRM"-->
<!--                            domain="[('th_apm_id', '=', False),('th_sale_order', '!=', 'crm')]"/>-->

                </group>
            </search>
        </field>
    </record>

    <record id="th_order_form_inherit_apm" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.apm</field>
        <field name="model">sale.order</field>
        <field name="priority">9999</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header/button[@name='action_confirm']" position="after">
                <field name="th_status_refund" invisible="1"/>
                <button name="action_show_popup_refund_apm" string="Hoàn tiền"
                        type="object" class="oe_highlight" title="Hoàn tiền"
                        attrs="{'invisible': ['|','|',('invoice_count', '=', 0),('th_sale_order', '!=', 'apm'),('th_status_refund','=', 'all_money')]}"/>
            </xpath>
            <xpath expr="//div[hasclass('oe_button_box')]" position="after">
                <field name="th_refund_invoice_apm" invisible="1"/>
                <widget name="web_ribbon" title="Đơn hàng chờ hoàn tiền"
                        attrs="{'invisible': [('th_refund_invoice_apm', '!=', True)]}"/>
                <widget name="web_ribbon" title="Đơn hàng đã hoàn 1 phần"
                        attrs="{'invisible': ['|','|',('th_status_refund', '!=', 'ex_money'),('th_refund_invoice_apm', '=', True),('th_sale_order', '!=', 'apm')]}"/>
                <widget name="web_ribbon" title="Đơn hàng đã hoàn toàn bộ"
                        attrs="{'invisible': ['|','|',('th_status_refund','!=', 'all_money'),('th_refund_invoice_apm', '=', True),('th_sale_order', '!=', 'apm')]}"/>
                <!--                invisible="not context.get('refund_tuition', False)"-->
            </xpath>
        </field>
    </record>

    <record id="sale_orders_action" model="ir.actions.act_window">
        <field name="name">Đơn hàng</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'create':0, 'invisible_button': True, 'view_apm': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('th_apm_id', '!=', False)]</field>
        <field name="search_view_id" ref="th_view_orders_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_view_order_form')}),
            ]"/>
    </record>
    <record id="sale_orders_action_b2b" model="ir.actions.act_window">
        <field name="name">Đơn hàng</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'create':0, 'invisible_button': True, 'view_apm': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('th_apm_id', '!=', False),('th_apm_id.th_ownership_unit_id.th_type', '=', 'other')]</field>
        <field name="search_view_id" ref="th_view_orders_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_view_order_form')}),
            ]"/>
    </record>

    <record id="sale_orders_detail_action" model="ir.actions.act_window">
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">form</field>
        <field name="view_ids"
               eval="[(5, 0, 0),
                     (0, 0, {'view_mode': 'form', 'view_id': ref('th_view_order_form')}),
                     ]"/>
    </record>

<!--      <record id="th_apm_need_payment_action" model="ir.actions.act_window">-->
<!--        <field name="name">Đơn hàng cần thanh toán</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">sale.order</field>-->
<!--        <field name="context">{'create':0,'invisible_button': True,'no_invoice': True,'no_button_refund': True}</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="search_view_id" ref="th_view_orders_account_search"/>-->
<!--        <field name="domain">[('th_apm_id', '!=', False),('th_paid_order', '=', False)]</field>-->
<!--        <field name="view_ids" eval="[(5, 0, 0),-->
<!--            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_account_tree')}),-->
<!--            (0, 0, {'view_mode': 'form', 'view_id': ref('view_order_account_form')}),-->
<!--            ]"/>-->
<!--    </record>-->
<!--        <record id="th_apm_account_paid_orders" model="ir.actions.act_window">-->
<!--        <field name="name">Đơn hàng đã thanh toán</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">sale.order</field>-->
<!--        <field name="context">{'create':0, 'invisible_button': True,'no_button_refund': True}</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="search_view_id" ref="th_view_orders_account_search"/>-->
<!--        <field name="domain">[('th_apm_id', '!=', False),('th_paid_order', '!=', False)]</field>-->
<!--       <field name="view_ids" eval="[(5, 0, 0),-->
<!--            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_account_tree')}),-->
<!--            (0, 0, {'view_mode': 'form', 'view_id': ref('view_order_account_form')}),-->
<!--            ]"/>-->
<!--    </record>-->

<!--Đơn hoàn tiền-->
    <record id="th_sale_orders_refund_invoice_apm" model="ir.actions.act_window">
        <field name="name">Đơn hàng cần hoàn tiền</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'create':0, 'refund_apm': True,'invisible_button': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="th_view_orders_account_search"/>
        <field name="domain">[('th_apm_id', '!=', False),('th_refund_invoice_apm', '!=', False)]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_account_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_order_account_form')}),
            ]"/>
    </record>

        <record id="th_sale_orders_refunded_invoice_apm" model="ir.actions.act_window">
        <field name="name">Đơn hàng đã hoàn tiền</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'create':0}</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="th_view_orders_account_search"/>
        <field name="domain">[('th_apm_id', '!=', False),('th_refunded_invoice_apm', '!=', False)]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_account_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_order_account_form')}),
            ]"/>
    </record>
    <record id="th_refund_invoice_apm" model="ir.actions.act_window">
        <field name="name">APM</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'create':0, 'refund_apm': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="th_view_orders_account_search"/>
        <field name="domain">[('th_apm_id', '!=', False),'|',('th_refunded_invoice_apm', '!=', False),('th_refund_invoice_apm', '!=', False)]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_account_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_order_account_form')}),
            ]"/>
    </record>

</odoo>

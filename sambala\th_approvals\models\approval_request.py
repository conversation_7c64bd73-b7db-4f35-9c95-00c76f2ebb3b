# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError


class ApprovalRequest(models.Model):
    _inherit = 'approval.request'

    th_template_name = fields.Char(related='category_id.th_template_name')
    th_sector_id = fields.Many2one(string='Lĩnh vực', comodel_name='th.approval.sector', tracking=True)
    request_status = fields.Selection([
        ('new', 'To Submit'),
        ('pending', 'Submitted'),
        ('approved', 'Approved'),
        ('completed', '<PERSON>àn thành'),
        ('refused', 'Từ chối'),
        ('cancel', 'Huỷ'),
    ], default="new", compute="_compute_request_status",
        store=True, tracking=True,
        group_expand='_read_group_request_status')
    request_owner_id = fields.Many2one(tracking=True)
    category_id = fields.Many2one(tracking=True)
    date = fields.Datetime(tracking=True)
    date_start = fields.Datetime(tracking=True)
    date_end = fields.Datetime(tracking=True)
    date_confirmed = fields.Datetime(tracking=True)
    location = fields.Char(tracking=True)
    partner_id = fields.Many2one(tracking=True)
    amount = fields.Float(tracking=True)
    reference = fields.Char(tracking=True)

    @api.onchange('th_sector_id')
    def _onchange_th_sector_id(self):
        if self.th_sector_id and self.category_id.th_sector_id != self.th_sector_id:
            self.category_id = False

    def action_download_template(self):
        if not (self.category_id.id and self.category_id.th_template_name):
            raise ValidationError(_("Template for this category is not set, please contact administrator!"))
        url = f"""web/content/?model=approval.category&id={self.category_id.id}&filename_field=th_template_name&field=th_template&download=true&filename={self.category_id.th_template_name}"""
        return {
            'name': 'Download',
            'type': 'ir.actions.act_url',
            'url': url,
            'target': 'self',
        }

    # Đánh dấu bản ghi hoàn thành
    def th_action_mark_completed(self):
        for rec in self:
            rec.write({
                'request_status': 'completed'
            })

    def action_confirm(self):
        # make sure that the manager is present in the list if he is required
        self.ensure_one()
        if self.category_id.manager_approval == 'required' and not self.env.user.has_group(
                'th_approvals.th_approvals_external_user'):
            employee = self.env['hr.employee'].search([('user_id', '=', self.request_owner_id.id)], limit=1)
            if not employee.parent_id:
                raise UserError(
                    'Yêu cầu này cần phải được quản lý phê duyệt. Không có quản lý nào liên kết với hồ sơ nhân viên của bạn.')
            if not employee.parent_id.user_id:
                raise UserError(
                    'Yêu cầu này cần phải được quản lý phê duyệt. Không có người dùng nào liên kết với quản lý của bạn.')
            if not self.approver_ids.filtered(lambda a: a.user_id.id == employee.parent_id.user_id.id):
                raise UserError(
                    'Yêu cầu này cần phải được quản lý phê duyệt. Quản lý của bạn không nằm trong danh sách người duyệt.')
        if len(self.approver_ids) < self.approval_minimum:
            raise UserError("Bạn phải thêm ít nhất %s người phê duyệt để xác nhận yêu cầu của bạn.",
                            self.approval_minimum)
        if self.requirer_document == 'required' and not self.attachment_number:
            raise UserError("Bạn phải đính kèm ít nhất một tài liệu.")

        approvers = self.approver_ids
        if self.approver_sequence:
            approvers = approvers.filtered(lambda a: a.status in ['new', 'pending', 'waiting'])

            approvers[1:].status = 'waiting'
            approvers = approvers[0] if approvers and approvers[0].status != 'pending' else self.env[
                'approval.approver']
        else:
            approvers = approvers.filtered(lambda a: a.status == 'new')

        approvers._create_activity()
        approvers.write({'status': 'pending'})
        self.write({'date_confirmed': fields.Datetime.now()})

    def write(self, vals):
        if 'reason' in vals:
            log_note = _("Mô tả thay đổi: %(old_value)s --> %(new_value)s", old_value=self.reason,
                         new_value=vals.get('reason'))
            self.message_post(body=log_note)
        result = super(ApprovalRequest, self).write(vals)
        return result

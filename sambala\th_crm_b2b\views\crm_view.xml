<odoo>

    <record id="th_crm_b2b_case_tree_view_oppor" model="ir.ui.view">
        <field name="name">th_crm_b2b_case_tree_view_oppor</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <tree string="Opportunities" sample="1" multi_edit="1">
<!--                <header>-->
<!--                    <button class="btn btn-primary" name="action_refresh_level" type="object" string="Refresh" groups="base.group_no_one"/>-->
<!--                </header>-->
                <field name="th_customer_code" readonly="1"/>
                <field name="th_customer_code_aum" readonly="1" invisible="1"/>
                <field name="partner_id" optional="show" string="Khách hàng"/>
                <field name="name" string="Cơ hội" optional="hide" readonly="1"/>
                <field name="th_last_check" optional="show" readonly="1"  widget="remaining_days"/>
                <field name="stage_id" optional="show" decoration-bf="1"/>
                <field name="phone" optional="show"/>
                <field name="email_from" optional="show"/>
                <field name="th_description" optional="show"/>
                <field name="th_status_detail_id" domain="[('th_status_category_id', '=', th_status_group_id), ('th_crm_level_ids', 'in', stage_id)]"
                       options="{'no_create': True}" optional="show"/>
                <field name="user_id" optional="show" domain="[('share', '=', False)]"/>
                <field name="th_customer_code_gf" invisible="1" widget="tree_url" readonly="1"/>
                 <field name="th_ownership_id" optional="hide" options="{'no_create': True}"/>
                <field name="th_origin_id" optional="hide" options="{'no_create': True}"/>
                <field name="th_admissions_region_id" string="Vùng tuyển sinh *" optional="hide"
                       options="{'no_create': True}" required="1"/>
                <field name="th_admissions_station_id" string="Trạm tuyển sinh *" required="1" optional="hide"
                       options="{'no_create': True}"/>
                <field name="th_channel_id" optional="hide" options="{'no_create': True}"/>
                <field name="state" optional="hide" options="{'no_create': True}"/>
                <field name="th_source_group_id" optional="hide" options="{'no_create': True}"/>
                <field name="th_source_name" optional="hide"/>
                <field name="th_graduation_system_id" optional="hide"/>
                <field name="th_date_of_delivery" optional="hide"/>
                <field name="th_decision_date" optional="hide"/>
                <field name="th_partner_referred_id" optional="hide"/>
                <field name="th_profile_status" optional="hide"/>
                <field name="th_major_id" optional="hide"/>
                <field name="th_status_group_id" optional="hide"/>
                <field name="th_registration_date" optional="hide"/>
                <field name="th_first_create_day" optional="hide"/>
                <field name="th_create_lead_date_getfly" optional="hide"/>
                <field name="th_level_up_date_getfly" optional="hide"/>
                <field name="th_l5b_aof_date_getfly" optional="hide"/>
                <field name="th_reuse_ccs_getfly" optional="hide"/>
                <field name="th_name_ccs_getfly" optional="hide"/>
                <field name="th_code_ccs_getfly" optional="hide"/>
                <field name="company_id" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="th_crm_lead_b2b_view" model="ir.ui.view">
        <field name="name">th.crm.lead.b2b.view</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <form class="o_lead_opportunity_form">
                <header>
                    <field name="th_storage" invisible="1"/>
                    <field name="stage_id" widget="th_statusbar" class="o_field_statusbar"
                           options="{'clickable': '1', 'fold_field': 'fold'}"
                           domain="[('th_type', '=', 'crm'), ('id', 'in', th_domain_stage_id)]" on_change="1"
                           attrs="{'invisible': ['|', '|', '|', '|', ('th_stage_auto', '!=', False),
                           ('th_is_a_duplicate_opportunity', '=', True), ('id', '=', False),
                           ('th_storage', '=', True), ('state', '=', 'transfer')]}"/>

                    <field name="th_is_a_duplicate_opportunity" invisible="1"/>
                    <field name="th_domain_stage_id" attrs="{'invisible': True, 'readonly': True}"/>
                    <field name="th_stage_auto" attrs="{'invisible': True, 'readonly': True}"/>
                    <field name="stage_id" widget="th_statusbar" class="o_field_statusbar"
                           domain="[('th_type', '=', 'crm'), ('id', 'in', th_domain_stage_id)]" on_change="1"
                           attrs="{'invisible': ['|', '|', ['th_stage_auto', '=', False], ['th_is_a_duplicate_opportunity', '=', True], ['th_stage_auto', '=', True]]}"
                    />
                    <button name="th_action_hand_over" string="Bàn giao Cơ hội" type="object" class="oe_highlight" title="Bàn giao Cơ hội"
                            attrs="{'invisible': ['|', '|', '|', '|', ['state', '=', 'transfer'], ['id', '=', 'False'],
                            ['th_dup_state', '=', 'processing'], ['th_selection_dup_result', '=', 'change'],
                            ['th_dividing_ring_id', '!=', False]]}"/>

                    <button name="th_send_noti_duplicate_lead_b2b" string="Khiếu nại" type="object" class="oe_highlight" title="Khiếu nại"
                            attrs="{'invisible': [['th_is_a_duplicate_opportunity', '=', False]]}"/>

                    <field name="stage_id" widget="statusbar" domain="[('th_type', '=', 'crm'), ('id', 'in', th_domain_stage_id)]" on_change="1"
                           attrs="{'invisible': [('th_is_a_duplicate_opportunity', '=', False), ('th_storage', '=', False), ('state', 'in', ['keep',False])]}"
                    />
                </header>
                <sheet>
                    <field name="readonly_domain" invisible="1"/>
                    <field name="active" invisible="1"/>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_profile_b2b" type="object" class="oe_stat_button" icon="fa-address-card"
                                context="{'partner_id': partner_id}"
                                attrs="{'invisible': [('th_student_profile_id', '=', False)]}">
                            <div class="o_stat_info">
                                <field name="th_student_profile_id" readonly="1" class="o_stat_value"
                                       options="{'no_open': True}"/>
                                <field name="th_profile_status" readonly="1"/>
                            </div>
                        </button>
                        <button name="action_open_profile_customer" type="object" class="oe_stat_button"
                                icon="fa-address-card">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Liên hệ
                                </span>
                            </div>
                        </button>
                        <button name="action_view_history" type="object" class="oe_stat_button" icon="fa-history"
                                attrs="{'invisible': [('th_is_a_duplicate_opportunity', '!=', True)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Lịch sử chăm sóc
                                </span>
                            </div>
                        </button>
                    </div>
                    <field name="th_dup_state" invisible="1"/>
                    <field name="th_selection_dup_result" invisible="1"/>
                    <field name="th_is_a_duplicate_opportunity" invisible="1"/>
                    <widget name="web_ribbon" title="Cơ hội đã bị trùng" bg_color="bg-danger"
                            attrs="{'invisible': ['|', ('th_dup_state', '!=', 'processing'), ('th_is_a_duplicate_opportunity', '=', False)]}"/>
                    <widget name="web_ribbon" title="Đã xử lý-Thua" bg_color="bg-warning"
                            attrs="{'invisible': [('th_selection_dup_result', '!=', 'change')]}"/>
                    <widget name="web_ribbon" title="Đã xử lý-Thắng" bg_color="bg-primary"
                            attrs="{'invisible': [('th_selection_dup_result', '!=', 'keep')]}"/>
                    <widget name="web_ribbon" title="Cơ hội trùng" bg_color="bg-danger" attrs="{'invisible': [('th_is_a_duplicate_opportunity', '=', False)]}"/>
                    <widget name="web_ribbon" title="Đã chuyển tư vấn" bg_color="bg-info"
                            attrs="{'invisible': ['|', '|', '|', ('th_selection_dup_result', '!=', False),
                            ('state', '=', 'keep'),
                            ('th_is_a_duplicate_opportunity', '=', True),
                            ('id', '=', False)
                            ]}"/>
                    <div class="oe_title">
                        <h1>
                            <field class="text-break" name="name" placeholder="vd: Giá Sản phẩm" readonly="1"
                                   required="1"/>
                        </h1>
                        <h2 class="d-flex gap-2 g-0 align-items-end pb-3">
                            <div>
                                <label for="th_customer_code" class="oe_edit_only pb-1"/>
                                <div class="d-flex align-items-end">
                                    <field name="th_customer_code" readonly="1"/>
                                </div>
<!--                                <label for="th_customer_code_aum" class="oe_edit_only pb-1"/>-->
<!--                                <div class="d-flex align-items-end">-->
<!--                                    <field name="th_customer_code_aum" readonly="1"/>-->
<!--                                </div>-->
                            </div>
                            <div>
                                <label for="th_last_check" class="oe_edit_only pb-1"/>
                                <div class="d-flex align-items-end">
                                    <field name="th_last_check" widget="remaining_days" readonly="1"/>
                                </div>
                            </div>
                        </h2>
                    </div>
                    <group>
                        <group name="opportunity_partner" attrs="{'invisible': [['type', '=', 'lead']]}">
                            <field name="th_check_crm_phone" string="Điện thoại" on_change="1" attrs="{'invisible': [['partner_id', '!=', False]]}"/>
                            <field name="th_check_crm_email" string="Email" on_change="1" attrs="{'invisible': [['partner_id', '!=', False]]}"/>
                            <field name="th_partner_domain" invisible="1"/>
                            <field name="email_state" on_change="1" invisible="1"/>
                            <field name="phone_state" on_change="1" invisible="1"/>
                            <field name="partner_email_update" invisible="1"/>
                            <field name="partner_phone_update" invisible="1"/>
                            <field name="th_domain_user_id" invisible="1"/>
                            <field name="is_partner_visible" invisible="1"/>
                            <field name="contact_name" invisible="1"/>
                            <field name="partner_name" invisible="1"/>
                            <field name="street" invisible="1"/>
                            <field name="street2" invisible="1"/>
                            <field name="city" invisible="1"/>
                            <field name="zip" invisible="1"/>
                            <field name="phone" invisible="1"/>
                            <field name="mobile" invisible="1"/>
                            <field name="email_from" invisible="1"/>
                            <field name="user_id" invisible="1"/>
                            <field name="team_id" invisible="1"/>
                            <field name="website" invisible="1"/>
                            <field name="lang_code" invisible="1"/>
                            <field name="title" invisible="1"/>
                            <field name="state_id" invisible="1"/>
                            <field name="country_id" invisible="1"/>
                            <field name="function" invisible="1"/>

                            <field name="email_from" attrs="{'invisible': [['partner_id', '=', False]], 'readonly': True}"/>
<!--                            <field name="phone" widget="phone" attrs="{'invisible': [['partner_id', '=', False]], 'readonly': True}"/>-->
                            <field name="phone" attrs="{'invisible': [['partner_id', '=', False]], 'readonly': True}"/>
<!--                            <field name="th_phone2" widget="phone" attrs="{'invisible': [['partner_id', '=', False]], 'readonly': True}"/>-->
                            <field name="th_phone2" attrs="{'invisible': [['partner_id', '=', False]], 'readonly': True}"/>
                            <field name="partner_id" widget="res_partner_many2one" string="Khách hàng *"
                                   context="{'res_partner_search_mode': type == 'opportunity' and 'customer' or False,
                                    'default_name': contact_name or partner_name,
                                    'default_street': street,
                                    'default_street2': street2,
                                    'default_city': city,
                                    'default_title': title,
                                    'default_state_id': state_id,
                                    'default_zip': zip,
                                    'default_country_id': country_id,
                                    'default_function': function,
                                    'default_phone': phone,
                                    'default_mobile': mobile,
                                    'default_email': email_from,
                                    'default_user_id': user_id,
                                    'default_team_id': team_id,
                                    'default_website': website,
                                    'default_lang': lang_code,
                                    'show_vat': True,
                                    'crm_contact': True,
                                    'default_phone': th_check_crm_phone,
                                    'default_email': th_check_crm_email,
                                }"
                                   options="{'no_quick_create': 1}" domain="[('id', 'in', [])]" on_change="1"
                                   attrs="{'readonly': [['id', '!=', False]], 'required': True}"
                            />
                            <field name="th_check_admission" invisible="1"/>
                            <field name="th_is_refunded_tuition" invisible="1"/>
                            <field name="th_required_fill" invisible="1"/>
                            <field name="th_admissions_region_id" string="Vùng tuyển sinh"
                                   options="{'no_open': True, 'no_create': True}" force_save="1"
                                   attrs="{'readonly': [['state', 'in', ['transfer']]],
                                   'required': [('th_is_a_duplicate_opportunity', '!=', True), ('th_required_fill', '=', True)]}"/>
                            <field name="th_admissions_station_id" string="Trạm tuyển sinh"
                                   options="{'no_open': True, 'no_create': True}" force_save="1"
                                   attrs="{'readonly': [['state', 'in', ['transfer']]],
                                   'required': [('th_is_a_duplicate_opportunity', '!=', True), ('th_required_fill', '=', True)]}"
                            />
                            <field name="th_major_id" force_save="1"
                                   options="{'no_open': True, 'no_create': True}"
                                   attrs="{'readonly': [['state', 'in', ['transfer']]]}"
                            />
                            <field name="th_graduation_system_id"
                                   options="{'no_open': True, 'no_create': True}"
                                   attrs="{'readonly': [['state', 'in', ['transfer']]]}"
                            />
                            <field name="lost_reason_id" attrs="{'invisible': [['active', '=', True]]}"/>
                            <field name="date_conversion" invisible="1"/>
                            <field name="user_company_ids" invisible="1"/>
                        </group>

                        <field name="type" on_change="1" invisible="1" required="1"/>
                        <group attrs="{'invisible': [('type', '=', 'lead')]}">
                            <field name="th_fees" attrs="{'invisible': [['th_fees', '=', False]], 'readonly': True}"/>
                            <field name="th_tuition_handed"
                                   attrs="{'invisible': [['th_tuition_handed', '=', False]], 'readonly': True}"/>
                            <field name="th_status_group_id" string="Nhóm tình trạng*"
                                   options="{'no_create': True, 'no_open': True}" force_save="1" on_change="1"
                                   attrs="{'readonly': [('state', 'in', ['transfer'])], 'required': True}"
                            />
                            <field name="th_status_detail_id" string="Trạng thái chi tiết*"
                                   options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible': [('th_status_group_id', '=', False)], 'readonly': [('state', 'in', ['transfer'])], 'required': True}"
                                   force_save="1"
                            />
                            <field name="th_level_up_date" force_save="1" readonly="1"/>
<!--                            <field name="create_date" string="Tạo vào" on_change="1" readonly="1"/>-->
                            <field name="th_registration_date" readonly="1"/>
                            <field name="th_check_admin_crm" invisible="1"/>
                            <field name="state" string="Loại chăm sóc" force_save="1" on_change="1" default="" required="1"
                                   attrs="{'readonly': [['id', '!=', False], ['th_check_admin_crm', '=', False]]}"/>
                            <field name="th_delivery_date" force_save="1" attrs="{'invisible': ['|', ('th_delivery_date', '=', False), ('id', '=', False)], 'readonly': True}"/>
                            <field name="th_domain_dividing" invisible="1"/>
                            <field name="th_dividing_ring_id" options="{'no_create': 1, 'no_open':1}" force_save="1"
                                   attrs="{'required': [('state', '=', 'transfer'), ('th_is_a_duplicate_opportunity', '=', False)],
                               'readonly': ['|', ('th_is_a_duplicate_opportunity', '=', True), ('th_dividing_ring_id', '!=', False), ('id', '!=', False)],
                               'invisible':[('state', '!=', 'transfer')]
                               }" domain="th_domain_dividing"/>
                            <field name="th_dup_state" readonly="1" groups="base.group_no_one"/>
                            <field name="th_selection_dup_result" readonly="1" groups="base.group_no_one"/>
                            <field name="th_crm_lead_b2b_id" readonly="1" groups="base.group_no_one"/>

                            <field name="user_id" context="{'default_sales_team_id': team_id}"
                                    options="{'no_create': 1, 'no_open':1}" on_change="1"
                                   attrs="{'readonly': [('state', 'in', ['transfer'])]}"
                            />
                        </group>

                    </group>
                    <notebook>
                        <page string="Thông tin khác" name="other_infor">
                            <group>
                                <field name="type" invisible="1"/>
                                <group string="Thông tin giới thiệu" name="other_info1">
                                    <field name="th_domain_own" invisible="1"/>
                                    <field name="th_ownership_id" required="1" domain="th_domain_own" options="{'no_open': True, 'no_create': True}"
                                           force_save="1" on_change="1" attrs="{'readonly': [['state', 'in', ['transfer']]]}"/>
                                    <field name="th_source_group_id" options="{'no_open': True, 'no_create': True}"
                                           force_save="1" attrs="{'required': [('th_is_a_duplicate_opportunity', '!=', True)]}"/>
                                    <field name="th_source_name"/>
                                    <field name="th_partner_referred_id" context="{'filter': True}" options="{'no_open': True, 'no_create': True}"
                                           force_save="1" on_change="1" readonly="0"/>
                                    <field name="th_affiliate_code" readonly="1"/>
                                </group>
                                <group string="Thông tin khác">
                                    <field name="th_origin_id"
                                           options="{'no_open': True, 'no_create': True}"
                                           domain="[('th_module_ids.name', 'in', ['CRM'])]" force_save="1" on_change="1"
                                           attrs="{'readonly': [['state', 'in', ['transfer']]]}"/>
                                    <field name="th_crm_job" force_save="1"
                                           attrs="{'readonly': [['state', 'in', ['transfer']]]}"/>
                                    <field name="th_channel_id"
                                           options="{'no_open': True, 'no_create': True}"
                                           force_save="1"
                                           attrs="{'readonly': [['state', 'in', ['transfer']]], 'required': [('th_is_a_duplicate_opportunity', '!=', True)]}"/>
                                    <field name="th_check_admin" invisible="1"/>
                                    <field name="th_self_lead" attrs="{'readonly': [('id', '!=', False), ('th_check_admin', '=', False)]}"/>
                                    <field name="th_form_name" readonly="1"/>
                                    <field name="th_uuid_form" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Thông tin nguồn"
                              attrs="{'invisible': [['th_utm_source', '=', False], ['th_utm_medium', '=', False], ['th_utm_campaign', '=', False], ['th_utm_term', '=', False], ['th_utm_content', '=', False]]}">
                            <group>
                                <field name="th_utm_source" readonly="1"/>
                                <field name="th_utm_medium" readonly="1"/>
                                <field name="th_utm_campaign" readonly="1"/>
                                <field name="th_utm_term" readonly="1"/>
                                <field name="th_utm_content" readonly="1"/>
                            </group>
                        </page>
                        <page name="info_partner" string="Thông tin liên hệ">
                            <group>
                                <group string="Contact Information">
                                    <field name="partner_name" on_change="1" invisible="1"/>
                                    <label for="street_page_lead" string="Address"/>
                                    <div class="o_address_format">
                                        <field name="street" id="street_page_lead" placeholder="Street..."
                                               class="o_address_street" readonly="1"/>
                                        <field name="street2" placeholder="Street 2..." class="o_address_street"
                                               invisible="1"/>
                                        <field name="th_ward_id" placeholder="Xã / Phường" class="o_address_ward"
                                               style="width:50%" options="{'no_create': True, 'no_open': True}"
                                               on_change="1" readonly="1"/>
                                        <field name="th_district_id" placeholder="Quận / Huyện"
                                               class="o_address_district" style="width:50%"
                                               options="{'no_create': True, 'no_open': True}"
                                               readonly="1"/>
                                        <field name="city" placeholder="City" class="o_address_city" readonly="1" invisible="1"/>
                                        <field name="zip" placeholder="ZIP" class="o_address_zip" on_change="1" readonly="1" invisible="1"/>
                                        <field name="state_id" class="o_address_state" placeholder="State"
                                               options="{'no_create': True,'no_open': True,}" style="width:100%" readonly="1"/>
                                        <field name="country_id" placeholder="Country" class="o_address_country"
                                               options="{'no_open': True, 'no_create': True}" on_change="1" readonly="1"/>
                                    </div>

                                    <label string="Hộ khẩu thường trú" for="th_street"/>
                                    <div class="o_address_format">
                                        <field name="th_street" placeholder="Địa chỉ..." class="o_address_street"
                                               readonly="1"/>
                                        <field name="th_ward_permanent_id" placeholder="Xã / Phường"  class="o_address_ward" style="width:50%"
                                               options="{'no_create': True, 'no_open': True}" readonly="1"/>
                                        <field name="th_district_permanent_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%"
                                               options="{'no_create': True, 'no_open': True}" readonly="1"/>
                                        <field name="th_state_id" class="o_address_state" style="width:100%"
                                               placeholder="Tỉnh/ Tp" options="{'no_open': True, 'no_quick_create': True}"
                                               context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"
                                               readonly="1"/>
                                        <field name="th_country_id" placeholder="Quốc gia" class="o_address_country" options="{'no_open': True, 'no_create': True}" readonly="1"/>
                                    </div>
                                </group>
                                <group class="mt48">
                                    <label for="contact_name_page_lead"/>
                                    <div class="o_row">
                                        <field name="contact_name" id="contact_name_page_lead" on_change="1" invisible="1"/>
                                    </div>
                                    <field name="title" placeholder="Title" domain="[]" options="{'no_open': True}" readonly="1"/>
                                    <field name="function" readonly="1"/>
                                    <field name="th_gender" readonly="1"/>
                                    <label for="th_place_of_birth_id"/>
                                    <div class="o_row">
                                        <field name="th_place_of_birth_id" options="{'no_open': True, 'no_create': True}" readonly="1"/>
                                        <label for="th_birthday"/>
                                        <field name="th_birthday" readonly="1"/>
                                    </div>
                                    <label for="th_ethnicity_id"/>
                                    <div class="o_row">
                                        <field name="th_ethnicity_id" options="{'no_open': True, 'no_create': True}" readonly="1"/>
                                        <label for="th_religion_id"/>
                                        <field name="th_religion_id" options="{'no_open': True, 'no_create': True}" readonly="1"/>
                                    </div>
                                </group>
                            </group>
                        </page>
                        <page string="Mô tả check trùng"
                              attrs="{'invisible': [('th_duplicate_description', '=', False)]}">
                            <field name="th_duplicate_description" readonly="1"/>
                        </page>
                        <page string="Mô tả" name="internal_notes">
                            <field name="th_description"/>
                        </page>
                        <page name="extra" string="Thông tin thêm"
                              attrs="{'invisible': [['type', '=', 'opportunity']]}">
                            <group>
                                <group string="Email">
                                    <field name="message_bounce" readonly="1"/>
                                </group>
                                <group string="Marketing" name="categorization">
                                    <field name="company_id" options="{'no_create': True}" on_change="1"
                                    />
                                    <field name="campaign_id"
                                           options="{'create_name_field': 'title', 'always_reload': True}"
                                    />
                                    <field name="medium_id"/>
                                    <field name="source_id"/>
                                    <field name="referred"/>
                                </group>
                                <group string="Phân tích">
                                    <field name="date_open" on_change="1" readonly="1"/>
                                    <field name="date_closed" on_change="1" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Data Getfly" name="data_getfly"
                              attrs="{'invisible': [('th_data_getfly', '=', False)]}">
                            <field name="th_data_getfly" readonly="1"/>
                        </page>
                        <page string="Dữ liệu Getfly">
                            <group>
                                <group>
                                    <field name="th_create_lead_date_getfly" readonly="1"/>
                                    <field name="th_level_up_date_getfly" readonly="1"/>
                                    <field name="th_l5b_aof_date_getfly" readonly="1"/>
                                </group>
                                <group>
                                    <field name="th_reuse_ccs_getfly" readonly="1"/>
                                    <field name="th_name_ccs_getfly" readonly="1"/>
                                    <field name="th_code_ccs_getfly" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_crm_b2b_lead_search_view" model="ir.ui.view">
        <field name="name">th_crm_duplicate_lead_search_view</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <search string="">
                <field name="phone_mobile_search"/>
                <field name="name" string="Cơ hội"/>
                <field name="th_customer_code" />
                <field name="th_customer_code_aum" invisible="1"/>
                <field name="email_from"/>
                <field name="user_id"/>
                <field name="partner_id" string="Khách hàng"/>
                <field name="th_ownership_id"/>
<!--                <field name="create_date"/>-->
                <field name="th_registration_date"/>
                <field name="th_source_group_id"/>
                <field name="th_source_name"/>
                <field name="th_origin_id"/>
                <field name="th_admissions_region_id"/>
                <field name="th_admissions_station_id"/>
                <group expand="0" string="Group By">
                    <!--                    <filter string="Ngày bị trùng" name="th_duplicated_date" domain="[]" context="{'group_by':'th_duplicated_date'}"/>-->
                </group>
                <searchpanel>
                    <field name="user_id" icon="fa-user-circle" enable_counters="1"/>
                    <field name="stage_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_status_detail_id" icon="fa-phone" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <!--View tất cả cơ hội trong kho lưu trữ-->
    <record id="th_crm_b2b_lead_all_archives_action" model="ir.actions.act_window">
        <field name="name">Kho tổng</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type','=','opportunity'), ('th_storage', '=', True)]</field>
        <field name="context">{'default_th_storage': True, 'create':0, 'edit':0, 'delete':0}</field>
        <field name="search_view_id" ref="th_crm_b2b_lead_search_view"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_crm_b2b_case_tree_view_oppor')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_crm_lead_b2b_view')}),
            ]"/>
    </record>

    <!--    View cơ hội trong kho lưu trữ theo trường học-->
    <record id="th_crm_b2b_lead_archives_action" model="ir.actions.act_window">
        <field name="name">Kho</field>
        <field name="res_model">th.origin</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{'create': False, 'archives': True, 'b2b': True, 'edit':0, 'delete':0}</field>
        <field name="domain">[('th_module_ids.name', '=', 'CRM')]</field>
        <field name="view_id" ref="th_crm.th_university_view_kanban"/>
    </record>

    <record id="th_crm_b2b_lead_action_archives" model="ir.actions.act_window">
        <field name="name">Kho lưu trữ</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type','=','opportunity'), ('th_storage', '=', True), ('th_origin_id', '=', active_id)]
        </field>
        <field name="context">{ 'create':0, 'edit':0, 'delete':0, 'invisible_university': True}</field>
        <field name="search_view_id" ref="th_crm_b2b_lead_search_view"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_crm_b2b_case_tree_view_oppor')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_crm_lead_b2b_view')}),
            ]"/>
    </record>

    <!--    View cơ hội đang trùng-->
    <record id="th_crm_b2b_duplicate_lead_act" model="ir.actions.act_window">
        <field name="name">Cơ hội trùng</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('type','=','opportunity'),('th_is_a_duplicate_opportunity', '=', True),
            ('th_storage','=', False)]
        </field>
        <field name="context">{'create':0, 'edit':0, 'delete':0, 'view_deplicate': True}</field>
        <field name="search_view_id" ref="th_crm_b2b_lead_search_view"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_crm_b2b_case_tree_view_oppor')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_crm_lead_b2b_view')}),
            ]"/>
    </record>

    <!--    View cơ hội đang chăm sóc theo trường-->
    <record id="th_crm_b2b_lead_university_view_kanban_main" model="ir.actions.act_window">
        <field name="name">Trường học</field>
        <field name="res_model">th.origin</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{'create': False, 'b2b': True}</field>
        <field name="domain">[('th_module_ids.name', '=', 'CRM')]</field>
        <field name="view_id" ref="th_crm.th_university_view_kanban"/>
    </record>

    <record id="th_crm_b2b_lead_action_pipeline" model="ir.actions.act_window">
        <field name="name">Cơ hội của tôi</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="th_crm_b2b_lead_search_view"/>
        <field name="domain">[
            ('type','=','opportunity'), ('th_origin_id', '=', active_id),
            ('th_storage', '=', False), ('th_is_a_duplicate_opportunity', '=', False)]
        </field>
        <field name="context">{
            'create':1, 'default_type': 'opportunity', 'default_th_origin_id': active_id,
            'b2b': True, 'default_state':'keep', 'default_th_ownership_id': False,'view_b2b':True
            }
        </field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_crm_b2b_case_tree_view_oppor')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_crm_lead_b2b_view')}),
            ]"/>
    </record>

    <!--    View tất cả cơ hội đang chăm sóc-->
    <record id="th_crm_b2b_lead_act" model="ir.actions.act_window">
        <field name="name">Tất cả cơ hội</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">crm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'default_state':'keep', 'create': 0, 'default_state':'keep', 'default_th_ownership_id': False}</field>
        <field name="domain">[('type','=','opportunity'),('th_is_a_duplicate_opportunity', '=', False), ('th_storage','=', False)]</field>
        <field name="search_view_id" ref="th_crm_b2b_lead_search_view"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_crm_b2b_case_tree_view_oppor')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_crm_lead_b2b_view')}),
            ]"/>
    </record>

    <!--    Popup bàn giao cơ hội-->
    <record id="th_crm_popup_view_form" model="ir.ui.view">
        <field name="name">th_crm_popup_view_form</field>
        <field name="model">crm.lead</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                         <field name="th_domain_dividing" invisible="1"/>
                        <field name="th_dividing_ring_id" domain="th_domain_dividing" required="1" options="{'no_create': 1, 'no_open':1}"/>
                    </group>
                    <footer>
                        <button name="th_accept_team" string="Xác nhận" type="object" class="btn btn-primary"/>
                        <button string="HỦY" class="oe_link" special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>
</odoo>

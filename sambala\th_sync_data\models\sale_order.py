from dateutil.relativedelta import relativedelta
import requests
from odoo import fields, models, api, _, exceptions
from odoo.exceptions import ValidationError
import xmlrpc.client
from odoo.exceptions import UserError
import json


class SaleOrder(models.Model):
    _inherit = 'sale.order'
    @api.model_create_multi
    def create(self, values):
        # Call the original create method to create the sale order
        sale_order = super(SaleOrder, self).create(values)
        for vals in values:
            if vals.get('th_order_vmc_id') and not self._context.get('th_test_import', False):
                for rec in sale_order:
                    if rec.th_sale_order == 'apm':
                        rec.action_confirm()
        return sale_order

    def write(self, vals):
        res = super(SaleOrder, self).write(vals)
        for rec in self:
            if vals.get('th_status', False) != 'draft' and vals.get('th_status', False)  and not self._context.get('th_test_import', False) and rec.th_origin_id.id == self.env.ref('th_setup_parameters.th_origin_vmc').id:
                rec.th_sync_status_sale_order()
            if rec.state == 'sale' and rec.order_line and rec.th_apm_id and not self._context.get(
                    'th_test_import', False) and not rec.th_order_vmc_id and rec.th_origin_id.id == self.env.ref('th_setup_parameters.th_origin_vmc').id and not rec.th_is_a_simple_lesson:
                if rec.th_apm_id.th_ecommerce_platform == True and rec.th_updated:
                    self.th_sync_order_vmc(rec)
                    rec.th_sync_status_sale_order()
                elif rec.th_apm_id.th_ecommerce_platform == False:
                    self.th_sync_order_vmc(rec)
            if vals.get('th_status', False) != 'draft' and vals.get('th_status',False) and not self._context.get('th_test_import', False) and rec.th_origin_id.id == self.env.ref(
                            'th_setup_parameters.th_origin_vstep').id:
                        rec.th_update_status_sale_order_vstep()
            if rec.state == 'sale' and not self._context.get('th_test_import', False)  and rec.th_origin_id.id == self.env.ref('th_setup_parameters.th_origin_vstep').id and rec.th_cohort_id and not rec.th_order_vmc_id:
                    self.th_apm_create_order_vstep(rec)
                    # rec.th_update_status_sale_order_vstep()


        return res

    def th_sync_order_vmc(self, res=None):
        vals = {}
        log = ""
        try:
            if res.th_order_vmc_id:
                return
            server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
                                                          order='id desc')
            if not server_api:
                raise ValidationError('Không tìm thấy server!')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api), allow_none=True)
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password

            for rec in res:
                th_partner_id = result_apis.execute_kw(db, uid_api, password, 'res.partner', 'search', [[['phone', '=', rec.partner_id.phone]]])
                if not th_partner_id:
                    th_partner_id = result_apis.execute_kw(db, uid_api, password, 'res.partner', 'create', [{
                        'phone': rec.partner_id.phone,
                        'email': rec.partner_id.email,
                        'name': rec.partner_id.name,
                        'th_crm_code': rec.partner_id.th_customer_code,
                    }])
                if not th_partner_id:
                    return False

                # vals['name'] = rec.name
                vals['th_crm_code'] = rec.partner_id.th_customer_code
                vals['partner_id'] = th_partner_id[0] if type(th_partner_id) == list else th_partner_id
                # vals['status'] = rec.th_status if  rec.th_status else 'draft'
                vals['status'] = 'draft'
                # vals['state'] = rec.state  if   rec.state  else 'draft'
                vals['state'] = 'draft'
                vals['customer_phone'] = rec.partner_id.phone
                vals['customer_email'] = rec.partner_id.email
                vals['is_vmc_sale'] = True
                # vals['payday'] = datetime.now()
                vals['note'] = rec.note
                # vals['pricelist_id'] = rec.pricelist_id.th_e2_product_price_list_id
                vals['order_sambala_id'] = rec.id
                new_line_id = [(5, 0, 0)]
                for line in rec.order_line:
                    if line.display_type != 'line_note' and line.display_type != 'line_section':
                        if line.reward_id and line.reward_id.th_2e_reward_loyalty_id:
                            reward = result_apis.execute_kw(db, uid_api, password, 'loyalty.reward', 'search_read',
                                                            [[['id', '=', line.reward_id.th_2e_reward_loyalty_id]]],
                                                            {'limit': 1})
                            if reward:
                                new_line_id.append((0, 0, {
                                    # 'product_template_id': line.reward_id.discount_line_product_id.id,
                                    'order_id': rec.th_order_vmc_id,
                                    'is_service': True,
                                    'name': line.name,
                                    'product_id': reward[0]['discount_line_product_id'][0] if reward[0] and reward[0][
                                    'discount_line_product_id'] else False,
                                    'product_uom_qty': line.product_uom_qty,
                                    # 'price_reduce': line.price_reduce,
                                    'price_unit': line.price_unit,
                                    'discount': line.discount,
                                    'price_subtotal': line.price_subtotal,
                                    'order_line_sambala_id': line.id, }))
                        else:
                            new_line_id.append((0, 0, {
                                'product_template_id': line.product_template_id.th_e2_product_temp_id if line.product_template_id else False,
                                'name': line.name,
                                'product_id': line.product_id.th_e2_product_pro_id if line.product_id else False,
                                'product_uom_qty': line.product_uom_qty,
                                'price_unit': line.price_unit,
                                'discount': line.discount,
                                'price_subtotal': line.price_subtotal,
                                'order_line_sambala_id': line.id, }))
                vals['order_line'] = new_line_id
                log += "___đã xong value___"
                th_order_vmc_id = result_apis.execute_kw(db, uid_api, password, 'sale.order', 'sambala_create_sale_order', [[],vals])
                log += "___th_order_vmc_id___" + str(th_order_vmc_id)
                if th_order_vmc_id and th_order_vmc_id.get("status", False) == 'success':
                    rec.sudo().write({
                        'th_order_vmc_id': th_order_vmc_id.get("th_order_vmc_id", False)
                    })
                    rec.message_post(body=th_order_vmc_id.get("message", False))
                elif th_order_vmc_id and th_order_vmc_id.get("status", False) == 'error':
                    rec.message_post(body=th_order_vmc_id.get("message", False))


        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e) + log,
                'th_record_id': str(res.ids),
                'th_input_data': str(vals),
                'th_function_call': str('th_sync_order_vmc'),
            })

    def th_sync_status_sale_order(self):
        log = ""
        order_ids = False
        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
                                                      order='id desc')
        try:
            if not server_api:
                raise ValidationError('Không tìm thấy server!')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password

            for rec in self:
                if rec.th_order_vmc_id:
                    if not rec.th_status:
                        rec.th_status = 'draft',
                    data_code_2e = result_apis.execute_kw(db, uid_api, password, 'wizard.cancel.order',
                                                          'update_status_apm', [[], rec.th_order_vmc_id, rec.th_status, rec.partner_id.th_customer_code])
                    log += "___đã xong data_code_2e" + str(data_code_2e)
                    if data_code_2e:
                        if data_code_2e['account']['status']:
                            self.sudo().write({
                                'th_account_status': data_code_2e['account']['status']})
                            log_note = _("%s") % (data_code_2e['account']['message'])
                            rec.message_post(body=log_note)
                        response = self.env["th.apm.active.account"].sudo().search([("th_sale_order_id", "=", self.id), ("th_stage", "!=", 'l4')])
                        log += "response" + str(response)
                        if response:
                            if data_code_2e['account']['status'] == 'error':
                                log_note = _("%s") % (data_code_2e['account']['message'])
                                response.message_post(body=log_note)
                        if data_code_2e['courses']:
                            for rec in response.th_apm_active_account_line_ids:
                                for res in data_code_2e['courses']:
                                    if rec.th_default_code == res['default_code']:
                                        log += "response" + str(rec.th_default_code)
                                        if res['status'] == 'success':
                                            rec.th_waiting_reason = res['message']
                                            rec.th_course_activation_status = 'opened'
                                        else:
                                            rec.th_waiting_reason = res['message']
                                            rec.th_course_activation_status = 'wait_for_open'

        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e) + log,
                'th_record_id': str(self.ids),
                'th_input_data': str('Không thể lấy dữ liệu do đang đẩy bằng self vui lòng xem lại các id đã log!'),
                'th_function_call': str('th_sync_status_sale_order'),
            })
        return True

    def action_open_reward_wizard(self):
        self.ensure_one()
        self._update_programs_and_rewards()
        claimable_rewards = self._get_claimable_rewards()
        if len(claimable_rewards) == 1:
            coupon = next(iter(claimable_rewards))
            if len(claimable_rewards[coupon]) == 1:
                self._apply_program_reward(claimable_rewards[coupon], coupon)
                return True
        elif not claimable_rewards:
            return True
        res = self.env['ir.actions.actions']._for_xml_id('sale_loyalty.sale_loyalty_reward_wizard_action')
        res['context'] = {
            'promotion': 'True',
        }
        return res

    def th_apm_create_order_vstep(self, rec):
        """
        Gọi API để tạo đơn hàng Vstep trên hệ thống 2E.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'vmc')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, rec, 'th_create_order')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/order"

        data = {
            "userinfo": {
                "name": rec.partner_id.name,
                "phone": rec.partner_id.phone,
                "username": rec.th_username,
                "crm_code": rec.partner_id.th_customer_code,
                "email": rec.partner_id.email or "",
                "dob": getattr(rec.partner_id, 'dob', ""),
                "institution": getattr(rec.partner_id, 'institution', ""),
                "student_code": getattr(rec.partner_id, 'student_code', ""),
                "class_name": getattr(rec.partner_id, 'class_name', ""),
            },
            "order_detail": {
                "order_type": "vstep" if rec.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vstep').id else "vmc",
                "status": rec.state,
                "cohort_code": rec.th_cohort_id.th_cohort_code,
                "pricelist_sambala_id": rec.pricelist_id.id,
                "order_sambala_id": rec.id,
                "ecm_source": getattr(rec, 'ecm_source', ""),
                "items": [
                    {
                        "default_code": line.product_id.default_code,
                        "quantity": line.product_uom_qty,
                        "discount": line.discount,
                        "is_service": line.product_id.type == 'service',
                        "subtotal": line.price_subtotal
                    }
                    for line in rec.order_line if line.product_id.default_code
                ],
            }
        }
        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = f"✅ Tạo đơn hàng thành công! (ID: {rec.id})"
                self.with_context(th_sync=True).write({
                    'th_order_vmc_id': response_data.get("order_e2_id")
                })
                self.message_post(body=msg)
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, data, 'th_create_order')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'th_create_order')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'th_create_order')
            return {"status_code": 500, "message": msg}

    def th_update_status_sale_order_vstep(self):
        """
        Cập nhật trạng thái đơn hàng trên hệ thống 2E thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'vmc')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            for rec in self:
                self._log_api('error', msg, rec, 'th_update_status_sale_order')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        for rec in self:
            if not rec.th_order_vmc_id:
                continue

            url = f"{th_api.th_url_api}{th_api.th_api_root}/order/update_status/{rec.id}"

            data = {
                "order_type": "vmc" if rec.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vmc').id else "vstep",
                "status": rec.th_status,
                "order_sambala_id": rec.id
            }

            try:
                response = requests.put(url, headers=headers, data=json.dumps(data))
                try:
                    response_data = response.json()
                except ValueError:
                    response_data = {}
                if response.status_code == 200:
                    msg = f"✅ Cập nhật trạng thái đơn hàng thành công! (ID: {rec.id}, Trạng thái: {rec.th_status})"
                    self.env['bus.bus']._sendone(
                        self.env.user.partner_id,
                        "simple_notification",
                        {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                    )
                    self._log_api('success', msg, data, 'th_update_status_sale_order')
                    courses = response_data.get('courses', [])
                    userinfor = response_data.get('userinfor',False)

                    if courses:
                        status_account = self.env["th.apm.active.account"].sudo().search([
                            ("th_sale_order_id", "=", rec.id),
                            ("th_stage", "!=", 'l4')
                        ])
                        if status_account:
                            all_lines_opened = True
                            for line in status_account.th_apm_active_account_line_ids:
                                combo_code = line.th_default_code
                                related_courses = [c for c in courses if c.get('course_combo_shortname') == combo_code]
                                if not related_courses:
                                    continue

                                all_success = all(c.get('status') == 'success' for c in related_courses)
                                if all_success:
                                    line.th_course_activation_status = 'opened'
                                    line.th_waiting_reason = 'Gán combo thành công!'
                                else:
                                    line.th_course_activation_status = 'wait_for_open'
                                    line.th_waiting_reason = 'Gán combo thất bài.Truy cập mô tả để biết thêm chi tiết!'
                                    all_lines_opened = False

                            # ✅ Cập nhật stage tổng thể
                            if all_lines_opened:
                                rec.write({'th_account_status': "success"})# Đã tạo tài khoản
                                status_account.th_description = response_data
                            else:
                                rec.write({'th_account_status': "error"})  # Kích hoạt lỗi
                                status_account.th_description = response_data
                    if userinfor:
                        message = userinfor.get('message', '')
                        if message and rec.th_account_status == "success":
                            status_account.message_post(body=f"📌 Thông báo từ hệ thống E2: {message} và gán khóa học thành công")
                            rec.message_post(
                                body=f"📌 Thông báo từ hệ thống E2: {message} và gán khóa học thành công")
                        if message and rec.th_account_status == "error":
                            status_account.message_post(body=f"📌 Thông báo từ hệ thống E2: {message} và gán khóa học lỗi")
                            rec.message_post(
                                body=f"📌 Thông báo từ hệ thống E2: {message} và gán khóa học lỗi")
                else:
                    msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                    self._log_api('error', msg, data, 'th_update_status_sale_order')
                    return {"status_code": response.status_code, "message": msg}

            except requests.exceptions.RequestException as e:
                msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
                self._log_api('error', msg, data, 'th_update_status_sale_order')
                return {"status_code": 500, "message": msg}

    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    free_product = fields.Boolean(string="Free Product", store=True)
    discount = fields.Float(
        string="Discount (%)",
        compute='_compute_discount',
        digits=(16, 3),
        store=True, readonly=False, precompute=True)

    @api.onchange('price_unit', 'discount')
    def _compute_price_reduce(self):
        for line in self:
            if line.reward_id:
                line.price_reduce = line.price_unit * (1.0 - line.discount / 100.0)
            else:
                try:
                    line = line.with_company(line.company_id)
                    pricelist_price = line._get_pricelist_price()
                    line.price_reduce = pricelist_price
                except:
                    line.price_reduce = 0

# Copyright 2022 ACSONE SA/NV
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
import warnings
from datetime import datetime, date
from enum import Enum
from typing import Annotated, Generic, List, Optional, TypeVar, Union

from pydantic import AliasChoices, BaseModel, ConfigDict, Field, computed_field, EmailStr
T = TypeVar("T")


class PagedCollection(BaseModel, Generic[T]):
    count: Annotated[
        int,
        Field(
            ...,
            description="Count of items into the system.\n "
            "Replaces the total field which is deprecated",
            validation_alias=AliasChoices("count", "total"),
        ),
    ]
    items: List[T]

    @computed_field()
    @property
    def total(self) -> int:
        return self.count

    @total.setter
    def total(self, value: int):
        warnings.warn(
            "The total field is deprecated, please use count instead",
            DeprecationWarning,
            stacklevel=2,
        )
        self.count = value


class Paging(BaseModel):
    limit: Optional[int] = None
    offset: Optional[int] = None


#############################################################
# here above you can find models only used for the demo app #
#############################################################
class DemoUserInfo(BaseModel):
    name: str
    display_name: str


class DemoEndpointAppInfo(BaseModel):
    id: int
    name: str
    app: str
    auth_method: str = Field(alias="demo_auth_method")
    root_path: str
    model_config = ConfigDict(from_attributes=True)


class DemoExceptionType(str, Enum):
    user_error = "UserError"
    validation_error = "ValidationError"
    access_error = "AccessError"
    missing_error = "MissingError"
    http_exception = "HTTPException"
    bare_exception = "BareException"


class User(BaseModel):
    username: str = None
    email: str


class ResponseMessage(BaseModel):
    message: str


class BackLink(BaseModel):
    link_tracker: str
    odoo_utm_params: dict
    referrer: str = None
    code: str = None


class PartnerData(BaseModel):
    name: str
    phone: str


class ResPartnerDatas(BaseModel):
    th_customer_code: Union[str, bool] | None = None
    name: Union[str, bool] | None = None
    phone: Union[str, bool] | None = None
    th_phone2: Union[str, bool] | None = None
    email: Union[str, bool] | None = None
    th_gender: Union[str, bool] | None = None
    th_birthday: Union[date, bool] | None = None
    th_place_of_birth_id: Union[int, bool] | None = None

    title: Union[int, bool] | None = None
    lang: Union[str, bool] | None = None
    function: Union[str, bool] | None = None
    th_citizen_identification: Union[str, bool] | None = None
    th_date_identification: Union[datetime, bool] | None = None
    th_place_identification: Union[str, bool] | None = None
    vat: Union[str, bool] | None = None

    street: Union[str, bool] | None = None
    th_ward_id: Union[int, bool] | None = None
    th_district_id: Union[int, bool] | None = None
    state_id: Union[int, bool] | None = None
    country_id: Union[str, bool] | None = None

    th_street: Union[str, bool] | None = None
    th_ward_permanent_id: Union[int, bool] | None = None
    th_district_permanent_id: Union[int, bool] | None = None
    th_state_id: Union[int, bool] | None = None
    th_country_id: Union[str, bool] | None = None

    th_ethnicity_id: Union[int, bool] | None = None
    th_religion_id: Union[int, bool] | None = None
    th_module_ids: Union[List[int], bool] | None = None


class CrmLeadDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_ownership_id: Union[int, bool] | None = None
    th_origin_id: Union[int, bool] | None = None
    th_source_name: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None
    th_major_id: Union[int, bool] | None = None
    type: str | None = None
    th_status_group_id: Union[int, bool] | None = None
    th_status_detail_id: Union[int, bool] | None = None
    th_channel_id: Union[int, bool] | None = None
    th_source_group_id: Union[int, bool] | None = None
    stage_id: Union[int, bool] | None = None
    state: Union[str, bool] | None = None
    th_last_check: Union[datetime, bool] | None = None
    th_partner_referred_id: Union[int, bool] | None = None
    th_admissions_station_id: Union[int, bool] | None = None
    th_admissions_region_id: Union[int, bool] | None = None
    th_level_up_date: Union[date, bool] | None = None
    th_graduation_system_id: Union[int, bool] | None = None
    th_self_lead: Union[str, bool] | None = None
    th_utm_source: Union[str, bool] | None = None
    th_utm_medium: Union[str, bool] | None = None
    th_utm_campaign: Union[str, bool] | None = None
    th_utm_term: Union[str, bool] | None = None
    th_utm_content: Union[str, bool] | None = None
    th_crm_job: Union[str, bool] | None = None
    partner_id: Union[int, bool] | None = None
    partner_info: ResPartnerDatas | None = None
    th_dividing_ring_id: Union[int, bool] | None = None
    th_training_system_id: Union[int, bool] | None = None
    tag_ids: Union[List[int], bool] | None = None


class MailMessageDatas(BaseModel):
    date: Union[datetime, bool] | None = None
    author_id: Union[int, bool] | None = None
    message_type: Union[str, bool] | None = None
    model: Union[str, bool] | None = None
    res_id: Union[int, bool] | None = None
    body: Union[str, bool] | None = None


class CareHistoryResponse(BaseModel):
    name: str | None = None
    preview: str | None = None


class duplicateLeadNotifyDatas(BaseModel):
    th_duplicate_type: Union[str, bool] | None = None
    th_description: Union[str, bool] | None = None
    res_id: Union[int, bool] | None = None


class APMLeadDatas(BaseModel):
    # name: Union[str, bool]
    th_ownership_unit_id: Union[int, bool] | None = None
    th_origin_id: Union[int, bool] | None = None
    th_description: Union[str, bool] | None = None
    th_status_category_id: Union[int, bool] | None = None
    th_status_detail_id: Union[int, bool] | None = None
    th_channel_id: Union[int, bool] | None = None
    th_source_group_id: Union[int, bool] | None = None
    th_stage_id: Union[int, bool] | None = None
    th_last_check: Union[datetime, bool] | None = None
    th_partner_reference_id: Union[int, bool] | None = None
    th_level_up_date: Union[date, bool] | None = None
    th_partner_id: Union[int, bool] | None = None
    th_campaign_id: Union[int, bool] | None = None
    # th_product_category_ids: Union[list[int], bool]
    th_product_ids: Union[list[int], bool] | None = None
    th_apm_team_id: Union[int, bool] | None = None
    partner_info: ResPartnerDatas | None = None


class ApmTraitValueDatas(BaseModel):
    name: Union[str, bool] | None = None


class ApmTraitDatas(BaseModel):
    name: Union[str, bool] | None = None
    th_origin_id: Union[bool, int] | None = None
    th_apm_trait_value_ids: Union[bool, list[ApmTraitValueDatas]] = None


class RecordDatas(BaseModel):
    id_b2b: Union[int, bool] | None = None
    th_data_crm: CrmLeadDatas | None = None
    th_data_apm: APMLeadDatas | None = None
    th_data_dup_CRM: duplicateLeadNotifyDatas | None = None
    th_data_res_partner: ResPartnerDatas | None = None
    th_data_mail: MailMessageDatas | None = None

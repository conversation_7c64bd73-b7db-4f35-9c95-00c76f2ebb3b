from odoo import fields, models, api
from odoo.exceptions import ValidationError, UserError
import requests
from datetime import datetime
import json

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })

    def write(self, vals):
        res = super(SaleOrder, self).write(vals)
        for rec in self:
            if vals.get('th_status', False) != 'draft' and vals.get('th_status', False) and not self._context.get(
                    'th_test_import', False) and rec.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vmc').id:
                rec.th_update_status_sale_order()
            if rec.state == 'sale' and rec.order_line and rec.th_apm_id and not self._context.get(
                    'th_test_import', False) and not rec.th_order_vmc_id and rec.th_origin_id.id == self.env.ref(
                'th_setup_parameters.th_origin_vmc').id and not rec.th_is_a_simple_lesson:
                if rec.th_apm_id.th_ecommerce_platform == True and rec.th_updated:
                    self.th_create_order(rec)
                elif rec.th_apm_id.th_ecommerce_platform == False:
                    self.th_create_order(rec)
            if vals.get('th_status', False) != 'draft' and vals.get('th_status', False) and not self._context.get(
                    'th_test_import', False) and rec.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vstep').id:
                rec.th_update_status_sale_order()
            if rec.state == 'sale' and not self._context.get('th_test_import',
                                                             False) and rec.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vstep').id and rec.th_cohort_id and not rec.th_order_vmc_id:
                self.th_create_order(rec)
        return res

    def th_create_order(self, rec):
        """
        Gọi API để tạo đơn hàng trên hệ thống 2E.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, rec, 'th_create_order')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/order"

        order_type = "vmc" if rec.th_origin_id.id == self.env.ref('th_setup_parameters.th_origin_vmc').id else "vstep"

        order_detail = {
            "order_type": order_type,
            "status": rec.state,
            "pricelist_sambala_id": rec.pricelist_id.id,
            "order_sambala_id": rec.id,
            "ecm_source": getattr(rec, 'ecm_source', ""),
            "items": [
                {
                    "default_code": line.product_id.default_code,
                    "quantity": line.product_uom_qty,
                    "discount": line.discount,
                    "is_service": line.product_id.type == 'service',
                    "subtotal": line.price_subtotal
                }
                for line in rec.order_line if not line.reward_id
            ],
        }

        if order_type == "vstep" and rec.th_cohort_id:
            order_detail["cohort_code"] = rec.th_cohort_id.th_cohort_code

        data = {
            "userinfo": {
                "name": rec.partner_id.name,
                "phone": rec.partner_id.phone,
                "username": rec.th_username,
                "crm_code": rec.partner_id.th_customer_code,
                "email": rec.partner_id.email or "",
                "dob": getattr(rec.partner_id, 'dob', ""),
                "institution": getattr(rec.partner_id, 'institution', ""),
                "student_code": getattr(rec.partner_id, 'student_code', ""),
                "class_name": getattr(rec.partner_id, 'class_name', ""),
            },
            "order_detail": order_detail
        }

        reward_ids = {line.reward_id.id for line in rec.order_line if line.reward_id}
        if reward_ids:
            discount_code_rules = self.env['loyalty.program'].search([
                ('reward_ids', 'in', list(reward_ids)),
                ('program_type', '=', 'promo_code')
            ]).mapped('rule_ids.code')
            data["order_detail"]["discount_code"] = discount_code_rules or None
            data["order_detail"]["reward_id"] = next(iter(reward_ids), None)

        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = f"✅ Tạo đơn hàng thành công! (ID: {rec.id})"
                self.with_context(th_sync=True).write({
                    'th_order_vmc_id': response_data.get("order_e2_id")
                })
                self.message_post(body=msg)
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                )
                self._log_api('success', msg, data, 'th_create_order')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'th_create_order')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'th_create_order')
            return {"status_code": 500, "message": msg}

    def th_update_status_sale_order(self):
        """
            Gửi yêu cầu cập nhật trạng thái đơn hàng lên hệ thống E2 (VMC/VSTEP).
            Update trạng thái kích hoạt tài khoản.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            for rec in self:
                self._log_api('error', msg, rec, 'th_update_status_sale_order')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        for rec in self:
            if not rec.th_order_vmc_id:
                continue

            url = f"{th_api.th_url_api}{th_api.th_api_root}/order/update_status/{rec.id}"
            order_type = "vmc" if rec.th_origin_id.id == self.env.ref(
                'th_setup_parameters.th_origin_vmc').id else "vstep"

            data = {
                "order_type": order_type,
                "status": rec.th_status,
                "order_sambala_id": rec.id
            }

            try:
                response = requests.put(url, headers=headers, data=json.dumps(data))
                try:
                    response_data = response.json()
                except ValueError:
                    response_data = {}

                if response.status_code == 200:
                    msg = f"✅ Cập nhật trạng thái đơn hàng thành công! (ID: {rec.id}, Trạng thái: {rec.th_status})"
                    self.env['bus.bus']._sendone(
                        self.env.user.partner_id,
                        "simple_notification",
                        {"title": "Thông báo", "message": msg, "sticky": False, "warning": False}
                    )
                    self._log_api('success', msg, rec, 'th_update_status_sale_order')

                    if order_type == 'vstep':
                        self.th_process_vstep_activation(rec, response_data)
                    else:
                        self.th_process_vmc_activation(rec, response_data)

                else:
                    msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                    self._log_api('error', msg, rec, 'th_update_status_sale_order')
                    return {"status_code": response.status_code, "message": msg}

            except requests.exceptions.RequestException as e:
                msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
                self._log_api('error', msg, rec, 'th_update_status_sale_order')
                return {"status_code": 500, "message": msg}

    def th_process_vmc_activation(self, rec, response_data):
        """

        Xử lý phản hồi kích hoạt từ VMC.
        :param rec: record sale order
        :param response_data: dữ liệu phản hồi từ hệ thống 2e
        """

        account_data = response_data.get('account', {})
        courses_data = response_data.get('courses', [])

        if account_data.get('status'):
            rec.sudo().write({'th_account_status': account_data['status']})
            if account_data.get('message'):
                rec.message_post(body=account_data['message'])

        status_account = self.env["th.apm.active.account"].sudo().search([
            ("th_sale_order_id", "=", rec.id),
            ("th_stage", "!=", 'l4')
        ])
        status_account.th_description = response_data
        if not status_account:
            return

        if account_data.get('status') == 'error' and account_data.get('message'):
            status_account.message_post(body=account_data['message'])

        if courses_data:
            for line in status_account.th_apm_active_account_line_ids:
                for course in courses_data:
                    if line.th_default_code == course.get('default_code'):
                        line.th_waiting_reason = course.get('message', '')
                        line.th_course_activation_status = 'opened' if course.get(
                            'status') == 'success' else 'wait_for_open'

    def th_process_vstep_activation(self, rec, response_data):
        """
            Xử lý kết quả gán combo từ API VSTEP cho đơn hàng.

            :param rec: record sale order
            :param response_data: dữ liệu phản hồi từ hệ thống 2e
        """
        courses = response_data.get('courses', [])
        userinfor = response_data.get('userinfor')
        status_account = self.env["th.apm.active.account"].sudo().search([
            ("th_sale_order_id", "=", rec.id),
            ("th_stage", "!=", 'l4')
        ])
        if not courses or not status_account:
            return

        all_lines_opened = True
        for line in status_account.th_apm_active_account_line_ids:
            combo_code = line.th_default_code
            related_courses = [c for c in courses if c.get('course_combo_shortname') == combo_code]
            if not related_courses:
                continue

            all_success = all(c.get('status') == 'success' for c in related_courses)
            if all_success:
                line.th_course_activation_status = 'opened'
                line.th_waiting_reason = 'Gán combo thành công!'
            else:
                line.th_course_activation_status = 'wait_for_open'
                line.th_waiting_reason = 'Gán combo thất bại. Truy cập mô tả để biết thêm chi tiết!'
                all_lines_opened = False

        rec.write({'th_account_status': "success" if all_lines_opened else "error"})
        status_account.th_description = response_data

        if userinfor:
            message = userinfor.get('message', '')
            if message:
                suffix = "thành công" if all_lines_opened else "lỗi"
                full_msg = f"📌 Thông báo từ hệ thống E2: {message} và gán khóa học {suffix}"
                status_account.message_post(body=full_msg)
                rec.message_post(body=full_msg)






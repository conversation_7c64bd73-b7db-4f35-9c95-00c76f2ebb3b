from odoo import fields, models, api
from odoo.exceptions import ValidationError, UserError
import requests
from datetime import datetime
import json
class ProductTemplate(models.Model):
    _inherit = 'product.template'
    is_combo_vstep = fields.Bo<PERSON>an("là combo của vstep")
    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })
    def write(self, values):
        """
         cập nhật sản phẩm trên hệ thống 2E khi có thay đổi.
        """
        rec = super(ProductTemplate, self).write(values)
        for res in self:
            preorder_value = None
            if res.preorder:
                if isinstance(res.preorder, datetime):
                    preorder_value = res.preorder.strftime("%m/%d/%Y %H:%M:%S")

            data = {
                "categ_sambala_id": res.categ_id.id if res.categ_id else None,
                "product_sambala_id": res.id,
                "type": res.type,
                "default_code": res.default_code if res.default_code else "",
                "description": res.description or "",
                "name": res.name,
                "list_price": res.list_price or 0,
                "sale_ok": res.sale_ok,
                "purchase_ok": res.purchase_ok,
                "state": "sambala_approved",
                "preorder": preorder_value if preorder_value else "",
                "is_approved": True,
                "active": res.active,
                "type_category": res.categ_id.name if res.categ_id else "default",
                "product_type": "vmc" if res.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vmc').id else "vstep",
                "is_combo": res.is_combo if hasattr(res, "is_combo") else False,
                "is_combo_vstep": res.is_combo_vstep,
                "product_pro_sambala_id": res.product_variant_id.id if res.product_variant_id else None
            }
            if res.th_e2_product_temp_id and not self._context.get('th_sync', False):
                res.update_product_template(res.id, data)
            elif not res.th_e2_product_temp_id and res.default_code:
                res.create_product_template(data)

        return rec

    def create_product_template(self, data):
        """
        Hàm cập nhật tạo sản phẩm trên hệ thống 2e thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'create_product_template')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_template"

        try:
            response = requests.post(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                self.with_context(th_sync=True).write({
                    'th_e2_product_temp_id': response_data.get('th_product_id')
                })

                product = self.env['product.product'].browse(self.product_variant_id.id)
                product.write({
                    'th_e2_product_pro_id': response_data.get('th_product_pro_id')
                })

                msg = "✅ Tạo sản phẩm thành công!"
                self.message_post(body=msg)
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, data, 'create_product_template')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'create_product_template')
                return {"status_code": response.status_code, "message": msg}

        except Exception as e:
            self.env.cr.rollback()
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            print(e)
            self._log_api('error', msg, data, 'create_product_template')
            return {"status_code": 500, "message": msg}

    def update_product_template(self, product_sambala_id, data):
        """
        Gửi yêu cầu API cập nhật thông tin sản phẩm trên hệ thống 2E theo `product_sambala_id`.
        """
        th_api = self.env['th.api.server'].search([
            ('state', '=', 'deploy'), ('th_type', '=', 'e2')
        ], limit=1, order='id desc')

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'update_product_template')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_template/{product_sambala_id}"

        try:
            response = requests.put(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = f"✅ Cập nhật sản phẩm {product_sambala_id} thành công!"
                self.message_post(body=msg)
                self._log_api('success', msg, data, 'update_product_template')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'update_product_template')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            self.env.cr.rollback()
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'update_product_template')
            return {"status_code": 500, "message": msg}

    def unlink(self):
        """
        Gọi API xóa sản phẩm trên hệ thống 2E trước khi xóa trong Odoo.
        """

        # Gọi API xóa trước khi thực hiện unlink()
        for sambala_id in self:
            if sambala_id:  # Chỉ gọi API nếu có product_sambala_id hợp lệ
                self.delete_product_template(sambala_id.id)

        # Xóa bản ghi trong Odoo
        return super().unlink()

    def delete_product_template(self, product_sambala_id):
        """
        Gửi yêu cầu API xóa sản phẩm theo `product_sambala_id` trên hệ thống 2E.
        """
        th_api = self.env['th.api.server'].search([
            ('state', '=', 'deploy'), ('th_type', '=', 'e2')
        ], limit=1, order='id desc')

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, product_sambala_id, 'delete_product_template')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_template/{product_sambala_id}"

        try:
            response = requests.delete(url, headers=headers)

            try:
                response_data = response.json()
            except ValueError:
                response_data = None

            if response.status_code == 200:
                msg = f"✅ Xóa sản phẩm {product_sambala_id} thành công!"
                self.message_post(body=msg)
                self._log_api('success', msg, product_sambala_id, 'delete_product_template')
            else:
                error_message = response_data.get('message', 'Không rõ lỗi') if response_data else "Lỗi không xác định"
                msg = f"⚠️ Lỗi từ API: {error_message}"
                self._log_api('error', msg, product_sambala_id, 'delete_product_template')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            self.env.cr.rollback()
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, product_sambala_id, 'delete_product_template')
            return {"status_code": 500, "message": msg}


class ProductCategory(models.Model):
    _inherit = 'product.category'
    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })
    @api.model
    def create(self, values):
        rec = super(ProductCategory, self).create(values)
        for res in rec:
            if not self._context.get('th_test_import', False) and res.env.ref(
                    'th_setup_parameters.th_apm_module').id in res.th_module_ids.ids:
                data = {
                    "categ_sambala_id": res.id,
                    "categ_name_sambala": res.name,
                    "categ_parent_id_sambala": res.parent_id.id,
                    "type_of_categ": res.categ_type
                }
                res.create_product_categ(data)
        return rec

    def write(self, values):
        # Add code here
        rec =  super(ProductCategory, self).write(values)
        for res in self:
            data = {
            "categ_sambala_id" : res.id,
            "categ_name_sambala" : res.name,
            "categ_parent_id_sambala" : res.parent_id.id,
            "type_of_categ": res.categ_type
            }
        if res and not self._context.get('th_test_import', False) and 'name' in values or 'parent_id' in values or 'type_of_categ' in values:
            res.update_product_categ(data,res.id)
        return rec
    def unlink(self):
        """
        Gọi API xóa sản phẩm trên hệ thống 2E trước khi xóa trong Odoo.
        """

        # Gọi API xóa trước khi thực hiện unlink()
        for sambala_id in self:
            if sambala_id:  # Chỉ gọi API nếu có product_sambala_id hợp lệ
                self.delete_product_categ(sambala_id.id)

        # Xóa bản ghi trong Odoo
        return super().unlink()

    def create_product_categ(self, data):
        """
        Gửi thông tin danh mục sản phẩm đến hệ thống 2e thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'create_product_categ')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_category"
        try:
            response = requests.post(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                self.with_context(th_sync=True).write({
                    'th_2e_id': response_data.get('product_categ')
                })
                msg = "✅ Tạo danh mục sản phẩm thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, data, 'create_product_categ')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'create_product_categ')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'create_product_categ')
            return {"status_code": 500, "message": msg}

    def update_product_categ(self, data, categ_sambala_id):
        """
        Cập nhật danh mục sản phẩm trên hệ thống 2e thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'update_product_categ')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_category/{categ_sambala_id}"
        try:
            response = requests.put(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = "✅ Sửa danh mục sản phẩm thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, data, 'update_product_categ')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'update_product_categ')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'update_product_categ')
            return {"status_code": 500, "message": msg}

    def delete_product_categ(self, categ_sambala_id):
        """
        Xóa danh mục sản phẩm trên hệ thống 2e thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, categ_sambala_id, 'delete_product_categ')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_category/{categ_sambala_id}"
        try:
            response = requests.delete(url, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = "✅ Xóa danh mục sản phẩm thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, categ_sambala_id, 'delete_product_categ')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, categ_sambala_id, 'delete_product_categ')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, categ_sambala_id, 'delete_product_categ')
            return {"status_code": 500, "message": msg}


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def _log_api(self, state, description, input_data, function_name):
        self.env['th.log.api'].create({
            'state': state,
            'th_model': self._name,
            'th_description': description,
            'th_input_data': str(input_data),
            'th_function_call': function_name,
            'is_log_fast_api': True,
        })
    def write(self, values):
        # Add code here
        rec =  super(ProductProduct, self).write(values)
        for res in self:
            data = {
            "name": res.name,
            "product_sambala_id": res.product_variant_id.id,
            "default_code": res.default_code,
            "product_pro_sambala_id": res.id,
            "product_type": "vmc" if res.th_origin_id.id == self.env.ref(
                    'th_setup_parameters.th_origin_vmc').id else "vstep",
            }
            if res.th_e2_product_pro_id and not self._context.get('th_test_import', False) and (
                    'name' in values or 'default_code' in values or 'product_type' in values
            ):
                res.update_product_product(data,res.id)
        return rec

    def update_product_product(self, data, product_id):
        """
        Cập nhật thông tin sản phẩm trên hệ thống Sambala thông qua API.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, data, 'update_product_product')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_product/{product_id}"

        try:
            response = requests.put(url, json=data, headers=headers)
            try:
                response_data = response.json()
            except ValueError:
                response_data = {}

            if response.status_code == 200:
                msg = "✅ Cập nhật sản phẩm thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, data, 'update_product_product')
            else:
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, data, 'update_product_product')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, data, 'update_product_product')
            return {"status_code": 500, "message": msg}

    def unlink(self):
        """
        Gọi API xóa biến sản phẩm trên hệ thống 2E trước khi xóa trong Odoo.
        """

        # Gọi API xóa trước khi thực hiện unlink()
        for sambala_id in self:
            if sambala_id:  # Chỉ gọi API nếu có product_sambala_id hợp lệ
                self.delete_product_product(sambala_id.id)

        # Xóa bản ghi trong Odoo
        return super().unlink()

    def delete_product_product(self, product_id):
        """
        Gọi API xóa sản phẩm trên hệ thống Sambala trước khi xóa trong Odoo.
        """
        th_api = self.env['th.api.server'].search(
            [('state', '=', 'deploy'), ('th_type', '=', 'e2')],
            limit=1, order='id desc'
        )

        if not th_api:
            msg = "Không tìm thấy cấu hình API server đang hoạt động."
            self._log_api('error', msg, product_id, 'delete_product_product')
            return {"status_code": 500, "message": msg}

        headers = {
            "api-key": th_api.th_api_key,
            "username": th_api.th_user_api,
            "Content-Type": "application/json"
        }

        url = f"{th_api.th_url_api}{th_api.th_api_root}/product_product/{product_id}"

        try:
            response = requests.delete(url, headers=headers)

            if response.status_code == 200:
                msg = "✅ Xóa sản phẩm thành công!"
                self.env['bus.bus']._sendone(
                    self.env.user.partner_id,
                    "simple_notification",
                    {
                        "title": "Thông báo",
                        "message": msg,
                        "sticky": False,
                        "warning": False
                    }
                )
                self._log_api('success', msg, product_id, 'delete_product_product')
            else:
                response_data = response.json()
                msg = f"⚠️ Lỗi từ API: {response_data.get('detail', 'Không rõ lỗi')}"
                self._log_api('error', msg, product_id, 'delete_product_product')
                return {"status_code": response.status_code, "message": msg}

        except requests.exceptions.RequestException as e:
            msg = f"❌ Lỗi hệ thống khi gọi API: {str(e)}"
            self._log_api('error', msg, product_id, 'delete_product_product')
            return {"status_code": 500, "message": msg}

<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="0">
    <record id="th_schedule_sync_fastapi_crm_update_an_hour" model="ir.cron">
        <field name="name">Schedule sync CRM an hour</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'crm.lead'), ('th_type_sync', '=', 'an_hour'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('crm.lead')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>
    <record id="th_schedule_sync_fastapi_crm_daily" model="ir.cron">
        <field name="name">Schedule sync CRM Daily Fields</field>
        <field name="model_id" ref="model_th_clipboard"/>
        <field name="state">code</field>
        <field name="code">model.search([('th_model_name', '=', 'crm.lead'), ('th_type_sync', '=', 'a_day'), ('th_status', '=', 'waiting'), ('th_system', '=', 'b2b')]).schedule_sync_fastapi('crm.lead')</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>
</odoo>

from odoo import fields, models, api

TH_PARTNER_ADDRESS_FIELDS_TO_SYNC = [
    'th_street',
    'th_ward_id',
    'th_district_id',
    'th_state_id',
    'th_country_id',
    'th_ethnicity_id',
    'th_religion_id',
    'th_gender',
    'th_birthday',
    'th_place_of_birth_id',
    'th_citizen_identification',
    'th_date_iden',
    'th_street_permanent',
    'th_ward_permanent_id',
    'th_district_permanent_id',
    'th_state_permanent_id',
    'th_country_permanent_id',
]
TH_VALUES_PARTNER_ADDRESS_FIELDS_TO_SYNC = {
    'th_street': 'street',
    'th_ward_id': 'th_ward_id',
    'th_district_id': 'th_district_id',
    'th_state_id': 'state_id',
    'th_country_id': 'country_id',
    'th_ethnicity_id': 'th_ethnicity_id',
    'th_religion_id': 'th_religion_id',
    'th_gender': 'th_gender',
    'th_birthday': 'th_birthday',
    'th_place_of_birth_id': 'th_place_of_birth_id',
    'th_citizen_identification': 'th_citizen_identification',
    'th_date_iden': 'th_date_identification',
    'th_street_permanent': 'th_street',
    'th_ward_permanent_id': 'th_ward_permanent_id',
    'th_district_permanent_id': 'th_district_permanent_id',
    'th_state_permanent_id': 'th_state_id',
    'th_country_permanent_id': 'th_country_id',
}



class ThStudentProfile(models.Model):
    _name = "th.student.profile"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Hồ sơ sinh viên"

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Hồ sơ", compute="_compute_partner_address_values", compute_sudo=True)
    th_partner_id = fields.Many2one(comodel_name="res.partner", string="Hồ sơ của", index=True)
    th_first_and_last_name = fields.Char(string="Họ và tên", compute="_compute_partner_address_values", inverse='_inverse_th_first_and_last_name',store=False, compute_sudo=True)
    th_date_of_delivery = fields.Date(string="Ngày bàn giao trường",tracking=True)
    th_check_date_of_delivery = fields.Boolean()
    th_date_of_receipt = fields.Date(string="Ngày nhận HS-TVTS",tracking=True)
    th_handover_status = fields.Selection(selection=[('not_handed', 'Chưa bàn giao'),
                                                     ('handed_over', 'Đã bàn giao'),
                                                     ('handing_over', 'Chờ bàn giao'),
                                                     ], string="Tình trạng bàn giao",tracking=True, default='not_handed')
    th_reason = fields.Text(string="Lý do",tracking=True)
    th_citizen_identification_submitted = fields.Char(string="CMT/CCCD đã nộp",tracking=True, compute_sudo=True)
    th_missing_file = fields.Char(string="HS.Thu Thiếu",tracking=True)
    th_profile_status = fields.Selection(selection=[('full', 'Đủ'), ('minimum', 'Đủ tối thiểu'), ('missing', 'Thiếu')], tracking=True, string="Tình trạng hồ sơ")
    th_cover_profile = fields.Char(string="Vỏ", tracking=True)
    th_profile_image = fields.Char(string="Ảnh", tracking=True)
    th_profile_degree = fields.Char(string="Bằng", tracking=True)
    th_profile_graduate = fields.Char(string="Chứng nhận tốt nghiệp", tracking=True)
    th_profile_transcript = fields.Char(string="Bảng điểm", tracking=True)
    th_school_profile = fields.Char(string="Học bạ", tracking=True)
    th_pdk = fields.Char(string="Phiếu đăng ký", tracking=True)
    th_birth_certificate = fields.Char(string="Giấy khai sinh", tracking=True)
    th_citizen_identification = fields.Char(string="CMT/ CCCD", tracking=True, compute="_compute_partner_address_values", inverse='_inverse_th_citizen_identification',store=True)
    th_curriculum_vitae = fields.Char(string="Sơ yếu lý lịch", tracking=True)
    th_score_transfer = fields.Char(string="Phiếu chuyển đổi kết quả học tập", tracking=True)
    th_other = fields.Char(string="Khác", tracking=True)
    th_combination_sub_id = fields.Many2one(comodel_name="th.combination.subjects", tracking=True, string="Tổ hợp môn")
    th_high_school_diploma_number = fields.Char(string="Số hiệu bằng THPT", tracking=True)
    th_high_school_ref_number = fields.Char(string="Số vào sổ THPT", tracking=True)
    th_high_school_graduation_date = fields.Char(string="Ngày tháng năm tốt nghiệp THPT", tracking=True)
    th_subject_score_1 = fields.Float(string="Điểm lớp 12. Môn 1", tracking=True)
    th_subject_score_2 = fields.Float(string="Điểm lớp 12. Môn 2", tracking=True)
    th_subject_score_3 = fields.Float(string="Điểm lớp 12. Môn 3", tracking=True)
    th_medium_score_in4 = fields.Float(string="Điểm TBC TL/Học tập.Thang 4", tracking=True)
    th_medium_score_in10 = fields.Float(string="Điểm TBC TL/Học tập.Thang 10", tracking=True)
    th_graduation_rank = fields.Char(string="Hạng tốt nghiệp", tracking=True)
    th_unit_for_work = fields.Char(string="Đơn vị công tác", tracking=True)
    th_awarding_diplomas = fields.Char(string="Nơi cấp bằng tốt nghiệp", tracking=True)
    th_major_studied = fields.Char(string="Chuyên ngành đã học", tracking=True)
    th_high_school = fields.Char(string="Trường THPT", tracking=True)
    th_graduation_year = fields.Char(string="Năm tốt nghiệp", tracking=True)
    th_form_of_train = fields.Char(string="Hình thức đào tạo", tracking=True)
    th_certificate_number = fields.Char(string="Số hiệu bằng", tracking=True)
    th_reference_number = fields.Char(string="Số vào sổ", tracking=True)
    th_job = fields.Char(string="Nghề nghiệp", tracking=True)
    th_reusable_source_name = fields.Char(string="Tên nguồn tái sử dụng", tracking=True)
    th_date_iden = fields.Date(string="Ngày cấp CMT/ CCCD", compute="_compute_partner_address_values", tracking=True, inverse='_inverse_th_date_iden', store=False, compute_sudo=True)

    th_street = fields.Char(compute="_compute_partner_address_values",
                            string="Địa chỉ",
                            inverse="_inverse_th_street",
                            tracking=True, store=False, compute_sudo=True)
    th_residential = fields.Char(string="Thôn/Xóm/Khu dân cư/Tổ dân phố",
                                 tracking=True)
    th_ward_id = fields.Many2one(comodel_name="th.country.ward",
                                 string="Phường/Xã",
                                 domain="[('th_district_id', '=?', th_district_id), ('th_district_id.th_state_id', '=?', th_state_id)]",
                                 compute="_compute_partner_address_values",
                                 inverse="_inverse_th_ward_id", store=False, compute_sudo=True)
    th_district_id = fields.Many2one(comodel_name="th.country.district",
                                     string="Quận/Huyện",
                                     domain="[('th_state_id', '=?', th_state_id)]",
                                     compute="_compute_partner_address_values",
                                     inverse="_inverse_th_district_id",
                                     store=False, tracking=True, compute_sudo=True)
    th_state_id = fields.Many2one(comodel_name="res.country.state",
                                  string="Tỉnh/Thành phố",
                                  domain="[('country_id', '=?', th_country_id)]", tracking=True,
                                  compute="_compute_partner_address_values",
                                  inverse="_inverse_th_state_id", store=False, compute_sudo=True)
    th_country_id = fields.Many2one(comodel_name="res.country",
                                    string="Quốc Gia",
                                    default=lambda x: x.env.ref('base.vn'), tracking=True,
                                    compute="_compute_partner_address_values",
                                    inverse="_inverse_th_country_id", store=False, compute_sudo=True)

    th_street_permanent = fields.Char(tracking=True, string="Địa chỉ (Hộ khẩu)",
                                      compute="_compute_partner_address_values",
                                      inverse="_inverse_th_street_permanent", store=False, compute_sudo=True)
    th_residential_permanent = fields.Char(string="Thôn/Xóm/Khu dân cư/Tổ dân phố (Hộ khẩu)",
                                           tracking=True)
    th_ward_permanent_id = fields.Many2one(comodel_name="th.country.ward",
                                           string="Phường/Xã (Hộ khẩu)",
                                           domain="[('th_district_id', '=?', th_district_permanent_id), ('th_district_id.th_state_id', '=?', th_state_permanent_id)]",
                                           tracking=True, compute="_compute_partner_address_values",
                                           inverse="_inverse_th_ward_permanent_id", store=False, compute_sudo=True)
    th_district_permanent_id = fields.Many2one(comodel_name="th.country.district",
                                               string="Quận/Huyện (Hộ khẩu)",
                                               domain="[('th_state_id', '=?', th_state_permanent_id)]", tracking=True,
                                               compute="_compute_partner_address_values",
                                               inverse="_inverse_th_district_permanent_id", store=False, compute_sudo=True)
    th_state_permanent_id = fields.Many2one(comodel_name="res.country.state",
                                            string="Tỉnh/TP (Hộ khẩu)",
                                            domain="[('country_id', '=?', th_country_permanent_id)]", tracking=True,
                                            compute="_compute_partner_address_values",
                                            inverse="_inverse_th_state_permanent_id", store=False, compute_sudo=True)
    th_country_permanent_id = fields.Many2one(comodel_name="res.country",
                                              string="Quốc gia (Hộ khẩu)", tracking=True,
                                              compute="_compute_partner_address_values",
                                              inverse="_inverse_th_country_permanent_id", store=False, compute_sudo=True)



    th_ethnicity_id = fields.Many2one(comodel_name="th.ethnicity", tracking=True, string="Dân tộc", compute="_compute_partner_address_values", inverse='_inverse_th_ethnicity_id',store=False, compute_sudo=True)
    th_religion_id = fields.Many2one(comodel_name="th.religion", tracking=True, string="Tôn giáo", compute="_compute_partner_address_values", inverse='_inverse_th_religion_id',store=False, compute_sudo=True)
    th_gender = fields.Selection(string="Giới tính", tracking=True, selection=[('male', 'Nam'), ('female', 'Nữ'), ('other', 'Khác'), ], compute="_compute_partner_address_values", inverse='_inverse_th_gender',store=False, compute_sudo=True)
    th_birthday = fields.Date(string="Ngày sinh", tracking=True, compute="_compute_partner_address_values", inverse='_inverse_th_birthday',store=True, compute_sudo=True)
    th_place_of_birth_id = fields.Many2one(comodel_name="res.country.state", tracking=True, string="Nơi sinh", domain="[('country_id.code', '=', 'VN')]", compute="_compute_partner_address_values", inverse='_inverse_th_place_of_birth_id',store=False, compute_sudo=True)

    th_father_name = fields.Char(string="Họ tên cha", tracking=True)
    th_father_job = fields.Char(string="Nghề nghiệp cha", tracking=True)
    th_father_birther = fields.Char(string="Ngày sinh cha", tracking=True)

    th_mother_name = fields.Char(string="Họ tên mẹ", tracking=True)
    th_mother_job = fields.Char(string="Nghề nghiệp mẹ", tracking=True)
    th_mother_birther = fields.Char(string="Ngày sinh mẹ", tracking=True)

    # th_university_id = fields.Many2one(comodel_name="th.university", string="Trường")
    th_origin_id = fields.Many2one(comodel_name="th.origin", string="Trường")
    th_decision_name = fields.Char(string="Tên quyết định trúng tuyển", tracking=True)
    th_decision_date = fields.Date(string="Ngày có quyết định trúng tuyển", tracking=True)
    th_student_code = fields.Char('Mã sinh viên', tracking=True)
    th_12_performance = fields.Char('Học lực lớp 12', tracking=True)
    th_12_conduct = fields.Char('Hạnh kiểm lớp 12', tracking=True)
    th_train_system = fields.Char('Hệ đào tạo', tracking=True)
    active = fields.Boolean(default=True, string="Lưu trữ")

    # def write(self, values):
    #     if values.get('th_handover_status') and values.get('th_handover_status', False) == 'handed_over':
    #         values['th_date_of_delivery'] = fields.Date.today()
    #     else:
    #         values['th_date_of_delivery'] = False
    #     res = super(ThStudentProfile, self).write(values)
    #     return res

    def _prepare_address_values_from_partner(self, partner):
        # Sync all address fields from partner, or none, to avoid mixing them.
        if any(partner[TH_VALUES_PARTNER_ADDRESS_FIELDS_TO_SYNC.get(f)] for f in TH_PARTNER_ADDRESS_FIELDS_TO_SYNC):
            values = {f: partner[TH_VALUES_PARTNER_ADDRESS_FIELDS_TO_SYNC.get(f)] for f in TH_PARTNER_ADDRESS_FIELDS_TO_SYNC}
        else:
            values = {f: self[f] for f in TH_PARTNER_ADDRESS_FIELDS_TO_SYNC}
        return values

    @api.depends('th_partner_id')
    def _compute_partner_address_values(self):
        """ Sync all or none of address fields """
        for lead in self:
            lead.name = f"Hồ sơ {lead.th_partner_id.name}" if lead.th_partner_id else False
            lead.th_first_and_last_name = lead.th_partner_id.name if lead.th_partner_id else False
            lead.update(lead._prepare_address_values_from_partner(lead.th_partner_id))

    def _inverse_th_street_permanent(self):
        for rec in self:
            if rec.th_street_permanent:
                rec.th_partner_id.th_street = rec.th_street_permanent

    def _inverse_th_ward_permanent_id(self):
        for rec in self:
            if rec.th_ward_permanent_id:
                rec.th_partner_id.th_ward_permanent_id = rec.th_ward_permanent_id.id

    def _inverse_th_district_permanent_id(self):
        for rec in self:
            if rec.th_district_permanent_id:
                rec.th_partner_id.th_district_permanent_id = rec.th_district_permanent_id.id

    def _inverse_th_state_permanent_id(self):
        for rec in self:
            if rec.th_state_permanent_id:
                rec.th_partner_id.th_state_id = rec.th_state_permanent_id.id

    def _inverse_th_country_permanent_id(self):
        for rec in self:
            if rec.th_country_permanent_id:
                rec.th_partner_id.th_country_id = rec.th_country_permanent_id.id
    def _inverse_th_street(self):
        for rec in self:
            rec.th_partner_id.street = rec.th_street

    def _inverse_th_ward_id(self):
        for rec in self:
            rec.th_partner_id.th_ward_id = rec.th_ward_id.id

    def _inverse_th_district_id(self):
        for rec in self:
            rec.th_partner_id.th_district_id = rec.th_district_id.id

    def _inverse_th_state_id(self):
        for rec in self:
            rec.th_partner_id.state_id = rec.th_state_id.id

    def _inverse_th_country_id(self):
        for rec in self:
            rec.th_partner_id.country_id = rec.th_country_id.id

    def _inverse_th_ethnicity_id(self):
        for rec in self:
            rec.th_partner_id.th_ethnicity_id = rec.th_ethnicity_id.id

    def _inverse_th_religion_id(self):
        for rec in self:
            rec.th_partner_id.th_religion_id = rec.th_religion_id.id

    def _inverse_th_gender(self):
        for rec in self:
            rec.th_partner_id.th_gender = rec.th_gender

    def _inverse_th_birthday(self):
        for rec in self:
            rec.th_partner_id.th_birthday = rec.th_birthday

    def _inverse_th_place_of_birth_id(self):
        for rec in self:
            rec.th_partner_id.th_place_of_birth_id = rec.th_place_of_birth_id.id

    def _inverse_th_first_and_last_name(self):
        for rec in self:
            rec.th_partner_id.name = rec.th_first_and_last_name

    def _inverse_th_citizen_identification(self):
        for rec in self:
            rec.th_partner_id.th_citizen_identification = rec.th_citizen_identification

    def _inverse_th_date_iden(self):
        for rec in self:
            rec.th_partner_id.th_date_identification = rec.th_date_iden

    @api.onchange('th_ward_id')
    def onchange_th_ward_id(self):
        if self.th_ward_id:
            self.th_district_id = self.th_ward_id.th_district_id.id
            self.th_state_id = self.th_district_id.th_state_id.id
            self.th_country_id = self.th_state_id.country_id.id

    @api.onchange('th_district_id')
    def onchange_th_district_id(self):
        if self.th_district_id:
            self.th_state_id = self.th_district_id.th_state_id.id
            self.th_country_id = self.th_state_id.country_id.id
        if self.th_district_id != self.th_ward_id.th_district_id:
            self.th_ward_id = False

    @api.onchange('th_state_id')
    def onchange_th_state_id(self):
        if self.th_state_id:
            self.th_country_id = self.th_state_id.country_id.id
        if self.th_state_id != self.th_district_id.th_state_id:
            self.th_district_id = False

    @api.onchange('th_ward_permanent_id')
    def onchange_th_ward_permanent_id(self):
        if self.th_ward_permanent_id:
            self.th_district_permanent_id = self.th_ward_permanent_id.th_district_id.id
            self.th_state_permanent_id = self.th_district_permanent_id.th_state_id.id
            self.th_country_permanent_id = self.th_state_permanent_id.country_id.id

    @api.onchange('th_district_permanent_id')
    def onchange_th_district_permanent_id(self):
        if self.th_district_permanent_id:
            self.th_state_permanent_id = self.th_district_permanent_id.th_state_id.id
            self.th_country_permanent_id = self.th_state_permanent_id.country_id.id
        if self.th_district_permanent_id != self.th_ward_permanent_id.th_district_id:
            self.th_ward_permanent_id = False

    @api.onchange('th_country_permanent_id')
    def onchange_th_country_permanent_id(self):
        if self.th_country_permanent_id != self.th_state_permanent_id.country_id:
            self.th_state_permanent_id = False

    @api.onchange('th_state_permanent_id')
    def onchange_th_state_permanent_id(self):
        if self.th_state_permanent_id:
            self.th_country_permanent_id = self.th_state_permanent_id.country_id.id
        if self.th_state_permanent_id != self.th_district_permanent_id.th_state_id:
            self.th_district_permanent_id = False

    @api.onchange('th_country_id')
    def onchange_th_country_id(self):
        if self.th_country_id != self.th_state_id.country_id:
            self.th_state_id = False

    @api.depends('th_medium_score_in10')
    def _computeth_medium_score_in4(self):
        for rec in self:
            rec.th_medium_score_in4 = rec.th_medium_score_in10/2.5 if rec.th_medium_score_in10 else False

    def _set_th_medium_score_in10(self):
        for rec in self:
            rec.th_medium_score_in10 = rec.th_medium_score_in4 * 2.5 if rec.th_medium_score_in4 else False
import xmlrpc

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ThLogAPI(models.Model):
    _name = 'th.log.api'
    _description = "Log API của odoo"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'th_model'
    _order = 'id DESC'

    th_model = fields.Char('Bảng')
    th_record_id = fields.Char('ID bản ghi')
    th_function_call = fields.Char('Hàm')
    state = fields.Selection(selection=[('error', 'Lỗi'), ('success', 'Thành công')])
    th_input_data = fields.Html('Dữ liệu đầu vào')
    th_description = fields.Html("Mô tả")
    is_log_fast_api = fields.<PERSON><PERSON><PERSON>("Là Log của Fast")
    th_time_response = fields.Char('Thời gian trả dữ liệu(s)')

    @api.model
    def create(self, values):
        # Add code here
        if values.get('th_description', False):
            values['th_description'] = values['th_description'].replace('\\n', '<br/>')
        return super(ThLogAPI, self).create(values)

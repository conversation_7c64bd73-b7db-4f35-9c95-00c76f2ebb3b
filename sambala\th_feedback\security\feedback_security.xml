<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Module Category -->
    <record id="module_category_feedback" model="ir.module.category">
        <field name="name">Quản lý feedback</field>
        <field name="description">Quản lý feedback từ khách hàng</field>
        <field name="sequence">7</field>
    </record>

    <record id="module_category_feedback_child" model="ir.module.category">
        <field name="name">Quản lý feedback</field>
        <field name="description">Quản lý feedback từ khách hàng</field>
        <field name="parent_id" ref="module_category_feedback"/>
    </record>

    <record id="group_feedback_handler" model="res.groups">
        <field name="name">Nhân viên xử lý lỗi</field>
        <field name="category_id" ref="module_category_feedback_child"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_feedback_classifier" model="res.groups">
        <field name="name">Nhân viên phân loại lỗi</field>
        <field name="category_id" ref="module_category_feedback_child"/>
        <field name="implied_ids" eval="[(4, ref('group_feedback_handler'))]"/>
    </record>

    <record id="group_feedback_category_manager" model="res.groups">
        <field name="name">Quản lý</field>
        <field name="category_id" ref="module_category_feedback_child"/>
        <field name="implied_ids" eval="[(4, ref('group_feedback_classifier'))]"/>
    </record>

    <!-- Định nghĩa group admin sau cùng vì nó phụ thuộc vào các groups khác -->
    <record id="group_feedback_admin" model="res.groups">
        <field name="name">Quản trị viên</field>
        <field name="category_id" ref="module_category_feedback_child"/>
        <field name="implied_ids" eval="[(4, ref('group_feedback_category_manager'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <!-- Rule cho Admin và Category Manager với Classifier -->
    <record id="rule_feedback_admin_manager" model="ir.rule">
        <field name="name">Admin/Manager: All Records</field>
        <field name="model_id" ref="model_th_feedback_ticket"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups"
               eval="[(4, ref('th_feedback.group_feedback_admin')),
                      (4, ref('th_feedback.group_feedback_category_manager')),
                      (4, ref('th_feedback.group_feedback_classifier'))]"/>
    </record>

    <!-- Rule cho Handler -->
    <record id="rule_feedback_handler" model="ir.rule">
        <field name="name">Handler: Assigned Records</field>
        <field name="model_id" ref="model_th_feedback_ticket"/>
        <field name="domain_force">['|', ('th_assign_id', '=', user.id), ('create_uid', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('th_feedback.group_feedback_handler'))]"/>
    </record>

    <!-- Rule cho user -->
    <record id="rule_feedback_reporter" model="ir.rule">
        <field name="name">Reporter: Own Records</field>
        <field name="model_id" ref="model_th_feedback_ticket"/>
        <field name="domain_force">['|', '|', ('create_uid', '=', user.id), ('th_partner_id.user_ids', 'in', user.id),
            ('th_partner_ids.user_ids', 'in', user.id)]
        </field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <!-- Base access rights for feedback models -->
    <record id="access_th_feedback_ticket_base" model="ir.model.access">
        <field name="name">th_feedback.ticket: Base Access</field>
        <field name="model_id" ref="model_th_feedback_ticket"/>
        <field name="group_id" ref="base.group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

    <!-- Configuration Menu Access -->
    <record id="access_th_feedback_category_base" model="ir.model.access">
        <field name="name">th_feedback.category: Base Access</field>
        <field name="model_id" ref="model_th_feedback_category"/>
        <field name="group_id" ref="base.group_user"/>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>

</odoo>
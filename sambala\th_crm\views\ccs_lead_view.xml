<odoo>
    <record id="th_ccs_lead_view_tree" model="ir.ui.view">
        <field name="name">th_ccs_lead_view_tree</field>
        <field name="model">ccs.lead</field>
        <field name="arch" type="xml">
            <tree string="" multi_edit="1">
                <header>
                    <button name="action_create_crm_lead_lst" string="Tạo cơ hội" type="object" class="btn-primary"/>
                    <button name="action_black_list_ccs_lead" string="Danh sách đen" type="object" class="btn-primary"/>
                    <button name="action_cancel_black_list_ccs_lead" string="Hủy danh sách đen" type="object" class="btn-primary"/>
                    <button name="action_archive_to_warehouse" string="Thu hồi về kho" type="object" class="btn-primary"/>
                </header>
                <field name="th_customer_code" readonly="1" optional="show"/>
                <field name="name" readonly="1" optional="show"/>
                <field name="th_partner_id" string="Tên li<PERSON>n hệ" readonly="1" optional="show"/>
                <field name="th_phone" readonly="1" optional="show"/>
                <field name="th_email" readonly="1" optional="show"/>
                <field name="th_origin_id" optional="show"/>
                <field name="th_reuse_origin_ids" optional="hide" widget="many2many_tags" options="{'no_create': True, 'no_open': True}"/>
                <field name="th_description" optional="hide" />
                <field name="th_source_code_ccs" optional="hide"/>
                <field name="th_source_code_reused" optional="hide"/>
                <field name="th_user_id" optional="show" readonly="1"/>
                <field name="create_date" optional="hide" string="Ngày tạo" readonly="1"/>
                <field name="th_stage_id" optional="hide" readonly="1"/>
                <field name="th_status_detail_id" optional="hide" readonly="1"/>
                <field name="th_admissions_station_id" optional="hide"/>
                <field name="th_ownership_id" optional="hide"/>
                <field name="th_source_group_id" optional="hide"/>
                <field name="th_channel_id" optional="hide"/>
                <field name="th_last_check" widget="remaining_days" readonly="1" optional="hide"/>
                <field name="th_lead_id" optional="hide" readonly="1"/>
                <field name="crm_stage_id" optional="hide" readonly="1"/>
                <field name="crm_user_id" optional="hide" readonly="1"/>
                <field name="th_domain_user_id" invisible="1"/>
                <field name="th_admissions_region_id" optional="hide"/>
                <field name="th_source_name" optional="hide"/>
                <field name="th_team_id" optional="hide"/>
                <field name="th_partner_referred_id" optional="hide"/>
                <field name="th_opportunity_ready" invisible="1"/>
                <field name="th_opportunity_ready_count" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="th_ccs_lead_view_form" model="ir.ui.view">
        <field name="name">th_ccs_lead_view_form</field>
        <field name="model">ccs.lead</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <field name="th_is_won" invisible="1"/>
                    <field name="th_opportunity_ready" invisible="1"/>
                    <field name="th_block_css_lead" invisible="1"/>
                    <field name="th_partner_id" invisible="1"/>
                    <button name="action_create_crm_lead_form" string="Tạo cơ hội"
                            type="object" class="oe_highlight" title="Tạo cơ hội"
                            attrs="{'invisible': ['|', ('th_is_won', '=', False),  ('th_opportunity_ready', '!=', False)]}"/>
                    <button name="action_black_list_ccs_lead" string="Danh sách đen"
                            type="object" class="oe_highlight" title="Danh sách đen" attrs="{'invisible': [('th_block_css_lead', '=', True)]}"/>
                    <button name="action_cancel_black_list_ccs_lead" string="Hủy danh sách đen" type="object" class="oe_highlight" attrs="{'invisible': [('th_block_css_lead', '!=', True)]}"/>
                    <button name="action_archive_to_warehouse" string="Thu hồi về kho" type="object" class="oe_highlight" attrs="{'invisible': [('th_block_css', '=', True)]}"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar" options="{'clickable': '1'}" attrs="{'invisible': [('th_block_css', '=', True)]}"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar" attrs="{'invisible': [('th_block_css', '!=', True)]}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_crm_lead"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible': [('th_opportunity_ready', '=', False)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Cơ hội CRM
                                </span>
                            </div>
                        </button>
                        <button name="action_open_ccs_partner" type="object" class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible' :['|',('th_partner_id', '=', False),('th_block_css', '=', True)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Khách hàng
                                </span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Bị block"
                                attrs="{'invisible': [('th_block_css', '=', False)]}"/>
                    <field name="th_block_css" invisible="1"/>
                    <widget name="web_ribbon" title="Đã tạo cơ hội"
                                attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                    <field name="th_opportunity_ready" invisible="1"/>
                    <div class="oe_title">
                            <h1><field class="text-break" name="name" placeholder="Tên liên hệ" attrs="{'readonly': [('th_is_won', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/></h1>
                            <h1><field class="text-break" name="th_partner_id" placeholder="Tên liên hệ" options="{'no_create': True, 'no_open': True}" attrs="{'invisible': [('th_partner_id', '=', False)]}"/></h1>
                            <h2 class="d-flex gap-2 g-0 align-items-end pb-3">
                                <div>
                                    <label for="th_last_check" class="oe_edit_only pb-1" />
                                    <div class="d-flex align-items-end">
                                        <field name="th_last_check" widget="remaining_days" readonly="1"/>
                                    </div>
                                </div>
                            </h2>
                            <div attrs="{'invisible': [('th_partner_id', '=', False)]}">
                                <label for="th_customer_code"/>
                                <h2><field name="th_customer_code" readonly="1"/></h2>
                            </div>
                        </div>
                    <group>
                        <group>
                            <field name="th_partner_id" invisible="1" options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_email_partner" widget="email" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
<!--                            <field name="th_phone_partner" widget="phone" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>-->
                            <field name="th_phone_partner" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
<!--                            <field name="th_phone2_partner" widget="phone" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>-->
                            <field name="th_phone2_partner" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_email" widget="email" attrs="{'readonly': [('th_block_css', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/>
<!--                            <field name="th_phone" widget="phone" attrs="{'readonly': [('th_block_css', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/>-->
                            <field name="th_phone" attrs="{'readonly': [('th_block_css', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/>
<!--                            <field name="th_phone2" widget="phone" attrs="{'readonly': [('th_block_css', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/>-->
                            <field name="th_phone2" attrs="{'readonly': [('th_block_css', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_major_ids" invisible="1" widget="many2many_tags" options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_major_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_origin_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"  domain="[('th_module_ids.name', 'in', ['CRM'])]"/>
                            <field name="th_lead_id" options="{'no_create': True, 'no_open': True}" readonly="1" attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                            <field name="crm_stage_id" options="{'no_create': True, 'no_open': True}" attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                            <field name="crm_user_id" options="{'no_create': True, 'no_open': True}" attrs="{'invisible': [('th_opportunity_ready', '=', False)]}"/>
                        </group>
                        <group>
                            <field name="th_status_detail_id" required="1" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_customer_attitude_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_reuse_origin_ids" widget="many2many_tags" domain="[('th_module_ids.name', 'in', ['CRM'])]" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_level_up_date" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_domain_user_id" invisible="1" />
                            <field name="th_source_name" attrs="{'readonly': ['|',('th_block_css', '=', True),('th_lead_id', '!=', False)]}"/>
                            <field name="th_dividing_ring_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_user_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}" domain="th_domain_user_id"/>
                            <field name="th_source_code_ccs" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_source_code_reused" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_partner_referred_id" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            <field name="th_ownership_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': ['|',('th_block_css', '=', True),('th_lead_id', '!=', False)]}"/>

                        </group>
                    </group>
                    <notebook>
                        <page name="description" string="Mô tả">
                            <field name="th_description" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                        </page>
                        <page name="info_partner" string="Thông tin liên hệ" attrs="{'invisible': [('th_partner_id', '!=', False)]}">
                            <group>
                                <group string="Thông tin liên hệ">
                                    <label string="Địa chỉ" for="th_street"/>
                                    <div class="o_address_format">
                                        <field name="th_street" placeholder="Địa chỉ..." class="o_address_street"/>
                                        <field name="th_ward_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_district_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_state_id" class="o_address_state" style="width:100%" placeholder="Tỉnh/ Tp" options="{'no_open': True, 'no_quick_create': True}" />
                                        <field name="th_country_id" placeholder="Quốc gia" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>
                                    </div>
                                    <label string="Hộ khẩu thường trú" for="th_street_permanent"/>
                                    <div class="o_address_format">
                                        <field name="th_street_permanent" placeholder="Địa chỉ..." class="o_address_street"/>
                                        <field name="th_ward_permanent_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_district_permanent_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%" options="{'no_create': True, 'no_open': True}"/>
                                        <field name="th_state_permanent_id" class="o_address_state" style="width:100%" placeholder="Tỉnh/ Tp" options="{'no_open': True, 'no_quick_create': True}"/>
                                        <field name="th_country_permanent_id" placeholder="Quốc gia" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>
                                    </div>
                                </group>
                                <group string="">
                                    <field name="th_title_id" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="th_function"/>
                                    <field name="th_gender"/>
                                    <label for="th_place_of_birth_id" options="{'no_create': True, 'no_open': True}"/>
                                    <div class="o_row">
                                        <field name="th_place_of_birth_id" options='{"no_open": True, "no_create": True}'/>
                                        <label for="th_birthday"/>
                                        <field name="th_birthday"/>
                                    </div>
                                    <label for="th_ethnicity_id"/>
                                    <div class="o_row">
                                        <field name="th_ethnicity_id" options='{"no_open": True, "no_create": True}'/>
                                        <label for="th_religion_id"/>
                                        <field name="th_religion_id" options='{"no_open": True, "no_create": True}'/>
                                    </div>
                                </group>
                            </group>
                        </page>
                        <page name="other_info" string="Thông tin khác">
                            <group string="Thông tin khác">
                                <field name="th_admissions_station_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_admissions_region_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_channel_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_source_group_id" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                                <field name="th_reuse_source" attrs="{'readonly': [('th_block_css', '=', True)]}"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_ccs_lead_view_search" model="ir.ui.view">
            <field name="name">th_ccs_lead_view_search</field>
            <field name="model">ccs.lead</field>
            <field name="priority">15</field>
            <field name="arch" type="xml">
                <search string="Search Opportunities">
                    <field name="th_phone"/>
                    <field name="name" string="Cơ hội" filter_domain="[
                        '|',
                        ('th_email', 'ilike', self),
                        ('name', 'ilike', self),]"/>
                    <separator/>
                    <filter string="Cơ hội của tôi" name="th_assigned_to_me"
                        domain="[('th_user_id', '=', uid)]"
                        help="Opportunities that are assigned to me"/>
                    <filter string="Chưa phân công" name="unassigned"
                        domain="[('th_user_id', '=', False)]" help="No salesperson"/>
                    <filter string="Đã tạo cơ hội" name="not_lead"
                        domain="[('th_lead_id', '!=', False)]" />
                    <filter string="Chưa tạo cơ hội" name="has_lead"
                        domain="[('th_lead_id', '=', False)]"/>
                    <filter string="Đã bị block" name="Blocked"
                            domain="[('th_block_css', '=', True), ('th_lead_id', '=', False)]"/>
                    <filter string="Chưa bị block" name="Not blocked"
                            domain="[('th_block_css', '=', False)]"/>
                    <filter string="Bị trùng cơ hội" name="Is dup"
                            domain="[('th_is_lead_dup', '=', True)]"/>
                    <filter string="Không bị trùng cơ hội" name="Is dup"
                            domain="[('th_is_lead_dup', '=', False)]"/>
                    <separator/>
                    <group expand="0" string="Group By" colspan="16">
                        <filter string="Người phụ trách" name="th_salesperson" context="{'group_by':'th_user_id'}"/>
                        <filter string="Đội chăm sóc" name="th_saleschannel" context="{'group_by':'th_team_id'}"/>
                        <filter string="Mối quan hệ" name="th_stage_id" context="{'group_by':'th_stage_id'}"/>
                        <filter name="th_last_check" string="Liên hệ lần cuối" context="{'group_by':'th_last_check:day'}"/>
                    </group>
                    <searchpanel>
                        <field name="th_user_id" icon="fa-user-circle" enable_counters="1"/>
                        <field name="th_stage_id" icon="fa-user-plus" enable_counters="1"/>
                        <field name="th_status_detail_id" icon="fa-phone" enable_counters="1"/>
                        <field name="th_customer_attitude_id" icon="fa-meh-o" enable_counters="1"/>
                    </searchpanel>
                </search>
            </field>
        </record>


    <record id="th_ccs_lead_action" model="ir.actions.act_window">
        <field name="name">Chăm sóc khách hàng</field>
        <field name="res_model">ccs.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'create':1,
            'default_th_origin_id': active_id,
            }
        </field>
        <field name="domain">[('th_block_css_lead', '=', False),('th_origin_id', '=', active_id)]</field>
        <field name="search_view_id" ref="th_crm.th_ccs_lead_view_search"/>
    </record>
    <record id="th_all_ccs_lead_action" model="ir.actions.act_window">
        <field name="name">Chăm sóc khách hàng</field>
        <field name="res_model">ccs.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{}</field>
        <field name="domain">[('th_block_css_lead', '=', False)]</field>
        <field name="search_view_id" ref="th_crm.th_ccs_lead_view_search"/>
    </record>

        <record id="th_ccs_lead_block_action" model="ir.actions.act_window">
        <field name="name">Danh sách đen</field>
        <field name="res_model">ccs.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{}</field>
        <field name="domain">[('th_block_css_lead', '=', True)]</field>
        <field name="search_view_id" ref="th_crm.th_ccs_lead_view_search"/>
    </record>

<!--view kanban CSKH theo trường-->
       <record id="th_university_of_ccs_view_kanban_action" model="ir.actions.act_window">
        <field name="name">Trường</field>
        <field name="res_model">th.origin</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{'create': 0, 'view_ccs': True}</field>
        <field name="domain">[('th_module_ids.name', '=', 'CRM')]</field>
        <field name="view_id" ref="th_university_view_kanban"/>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
            </p>
            <p>
                <!-- More details about what a user can do with this object will be OK -->
            </p>
        </field>
    </record>

    <record id="th_action_assign_user_ccs" model="ir.actions.act_window">
        <field name="name">Chia cơ hội CSKH</field>
        <field name="res_model">crm.lead.reuse</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context" eval="{}"/>
        <field name="view_ids" eval="[(5, 0, 0),
                    (0, 0, {'view_mode': 'form', 'view_id': ref('lead_reuse_ccs_view_form')})]"/>
        <field name="binding_model_id" ref="model_ccs_lead"/>
        <field name="binding_view_types">list</field>
    </record>

</odoo>

from odoo import fields, models, api
from datetime import date

class ThStudentProfile(models.Model):
    _inherit = "th.student.profile"

    # def _th_domain_place_of_birth(self):
    #     state_id = self.env['res.country.state'].search([]).filtered(lambda u: u.country_id == self.env.ref('base.vn'))
    #     return [('id', 'in', state_id.ids)]

    # def _th_module_crm(self):
    #     crm_origin_ids = self.env.ref('th_setup_parameters.th_crm_module')
    #     return [('th_module_ids', 'in', crm_origin_ids.ids)]

    th_lead_id = fields.Many2one(comodel_name="crm.lead", string="C<PERSON> hội", ondelete='cascade')
    th_lead_ids = fields.One2many(comodel_name="crm.lead",inverse_name="th_student_profile_id", string="C<PERSON> hội")
    th_customer_code = fields.Char(string="<PERSON><PERSON> kh<PERSON> hàng", related='th_lead_id.th_customer_code', store=True)
    th_origin_id = fields.Many2one(comodel_name="th.origin", string="Trường", compute="_compute_profile_th_origin_id", store=True, compute_sudo=True)
    th_origin_id_compute = fields.Many2one(comodel_name="th.origin",  string="Trường", compute="_compute_profile_th_origin_id", compute_sudo=True)
    th_profile_batches_id = fields.Many2one(comodel_name="th.profile.batches", tracking=True, string="Đợt bàn giao")
    th_partner_id = fields.Many2one(comodel_name="res.partner", string="Hồ sơ của", index=True, required=True)
    th_date_of_receipt = fields.Date(string="Ngày nhận HS-TVTS", tracking=True)
    th_code_getfly = fields.Char(string="Mã KH Getfly", copy=False)
    stage_id = fields.Many2one(string="Mối quan hệ", related='th_lead_id.stage_id', store=True)
    th_status_group_id = fields.Many2one(string="Nhóm tình trạng cơ hội", related='th_lead_id.th_status_group_id', store=True)
    is_refund_opportunity = fields.Boolean(string='Là cơ hội hoàn tiền', tracking=True,
                                           related='th_lead_id.th_is_refunded_tuition')
    th_name = fields.Char(string='Tên cơ hội', related='th_lead_id.name')
    th_last_check = fields.Datetime(string="Liên hệ lần cuối", default=lambda self: fields.Datetime.now(),
                                    tracking=True)
    th_status_detail_id = fields.Many2one(string="Tình trạng gọi", tracking=True, comodel_name="th.status.category")# tình trạng gọi này tạm thời bỏ
    th_calling_status_id = fields.Many2one(comodel_name="th.calling.status", string="Tình trạng gọi hồ sơ", tracking=True)
    th_handover_list_id = fields.Many2one('th.profile.handover.session', copy=False, string="Xét duyệt bàn giao hồ sơ")
    th_handover_session = fields.Char(string="Đợt bàn giao hồ sơ", compute="get_handover_session_name", copy=False)
    th_check_handover = fields.Boolean(string='Chờ bàn giao', copy=False)
    th_selected_handover = fields.Boolean(string='Hồ sơ được chọn', copy=False)
    th_partner_name = fields.Char(string='Họ và tên', related='th_partner_id.name')
    th_partner_phone = fields.Char(string='SĐT', related='th_partner_id.phone')
    th_partner_phone2 = fields.Char(string='SĐT2', related='th_partner_id.th_phone2')
    th_partner_email = fields.Char(string='Email', related='th_partner_id.email')
    th_partner_birthday = fields.Date(string='Ngày tháng năm sinh', related='th_partner_id.th_birthday', store=True)
    th_admissions_region_id = fields.Many2one(string='Vùng tuyển sinh', copy=False, related="th_lead_id.th_admissions_region_id")
    th_admissions_station_id = fields.Many2one(string='Trạm tuyển sinh', copy=False, related="th_lead_id.th_admissions_station_id", store=True)
    th_major_id = fields.Many2one(string='Ngành đăng ký', copy=False, related="th_lead_id.th_major_id", store=True)
    user_id = fields.Many2one(string='Người phụ trách', copy=False, related="th_lead_id.user_id", store=True)
    th_graduation_system_id = fields.Many2one(string='Hệ tốt nghiệp', copy=False, related="th_lead_id.th_graduation_system_id")
    th_training_system_id = fields.Many2one(string='Hệ đào tạo', copy=False, related="th_lead_id.th_training_system_id")
    th_customer_code_gf = fields.Char(string="Mã Khách Hàng Getfly")
    th_status_group_lead_id = fields.Many2one(related='th_lead_id.th_status_group_id', string="Nhóm tình trạng", copy=False)
    th_status_detail_lead_id = fields.Many2one(related='th_lead_id.th_status_detail_id', string="Trạng thái chi tiết", copy=False)
    th_settlement_date = fields.Date(string="Ngày thanh toán", related='th_lead_id.th_settlement_date', store=True)
    th_check_dup_profile = fields.Boolean(string="Check hồ sơ trùng")
    th_level_up_date_getfly = fields.Date(string="Ngày dự kiến lên L8", related='th_lead_id.th_level_up_date_getfly', store=True)
    th_l8_aof_date = fields.Date(string="Ngày lên L8-AOF", related='th_lead_id.th_l5b_aof_date_getfly', store=True)
    th_customer_code_aum = fields.Char(string='Mã khách hàng', related='th_lead_id.th_customer_code_aum', store=True)
    th_handover_status = fields.Selection(selection='_get_new_th_handover_status',
                                          string="Tình trạng bàn giao",
                                          tracking=True,
                                          )
    @api.model
    def _get_new_th_handover_status(self):

        selection = [('not_handed', 'Chưa bàn giao'),
                    ('handed_over', 'Đã bàn giao'),
                    ('handing_over', 'Chờ bàn giao')]

        return selection

    @api.depends('th_lead_id')
    def _compute_profile_th_origin_id(self):
        for rec in self:
            rec.th_origin_id = False
            rec.th_origin_id_compute = False
            if rec.th_lead_id:
                rec.th_origin_id = rec.th_lead_id.th_origin_id.id
                rec.th_origin_id_compute = rec.th_lead_id.th_origin_id.id

    def action_view_crm_lead(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Cơ hội CRM',
            'view_mode': 'tree,form',
            'res_model': 'crm.lead',
            'target': 'current',
            'domain': [('id', 'in', self.th_lead_ids.ids)],
            'context': {'create': False}
        }

    def th_action_check_dup(self):
        for rec in self:
            rec = rec.sudo()
            check_dup_rec = self.env['th.student.profile'].sudo().search([('th_partner_id', '=', rec.th_partner_id.id), ('th_origin_id', '=', rec.th_origin_id.id)])
            if len(check_dup_rec) > 1:
                for record in check_dup_rec:
                    record = record.sudo()
                    record.th_check_dup_profile = True

    @api.depends('th_partner_id')
    def _compute_partner_address_values(self):
        """ Sync all or none of address fields """
        for lead in self:
            lead.name = f"Hồ sơ {lead.th_partner_id.name}" if lead.th_partner_id else False
            lead.th_first_and_last_name = lead.th_partner_id.name if lead.th_partner_id else False
            lead.update(lead._prepare_address_values_from_partner(lead.th_partner_id))

    @api.model_create_multi
    def create(self, vals_list):
        # Add code here
        res = super(ThStudentProfile, self).create(vals_list)
        for rec in res:
            if rec.th_lead_id.th_check_admission == True:
                rec.th_date_of_delivery = rec.th_lead_id.th_admission_list_id.create_date
                rec.th_handover_status = 'handed_over'
            if rec.th_date_of_delivery:
                rec.th_check_date_of_delivery = True
            rec.th_lead_id.write({'th_student_profile_id': res.id})
            if rec.th_lead_id.th_auto_next_level():
                rec.th_lead_id.stage_id = rec.th_lead_id.th_auto_next_level().th_crm_level.id
        return res

    def write(self, values):
        if not values.get('th_check_dup_profile', False) or not values.get('active', False):
            if not self._context.get('update_import', False):
                values['th_last_check'] = fields.Date.today()
        if values.get('th_date_of_delivery', ''):
            values['th_check_date_of_delivery'] = True
        res = super(ThStudentProfile, self).write(values)
        if values.get('th_profile_status') or 'th_profile_status' in values or values.get('th_date_of_delivery'):
            for rec in self:
                if rec.th_lead_id.th_auto_next_level():
                    rec.th_lead_id.stage_id = rec.th_lead_id.th_auto_next_level().th_crm_level.id
        if values.get('th_profile_batches_id') or 'th_profile_batches_id' in values or values.get(
                'th_profile_batches_id'):
            for rec in self:
                if rec.th_lead_id:
                    rec.th_lead_id.th_profile_batches_id = rec.th_profile_batches_id.id
        return res

    @api.onchange('th_profile_status')
    def _check_profile_status_for_date_of_receipt(self):
        for rec in self:
            if rec.th_profile_status in ('full', 'minimum') :
                rec.th_date_of_receipt = fields.Date.today()
            else:
                rec.th_date_of_receipt = False

    def change_handover_status(self):
        for rec in self:
            if rec.th_handover_status != 'handed_over':
                rec.th_handover_status = 'handed_over'
                rec.th_check_handover = True
                if rec.th_handover_list_id.th_date_of_delivery:
                    rec.th_date_of_delivery = rec.th_handover_list_id.th_date_of_delivery
                else:
                    rec.th_date_of_delivery = date.today()
        return True

    def return_handover_status(self):
        for rec in self:
            if rec.th_handover_status == 'handed_over':
                rec.th_handover_status = 'handing_over'
                rec.th_date_of_delivery = False
                rec.th_check_handover = False

    def th_action_select_profile_handover(self):
        for rec in self:
            rec.th_selected_handover = True

    def th_action_cancel_select_profile_handover(self):
        for rec in self:
            rec.th_selected_handover = False
            rec.th_handover_list_id = False

    @api.model
    def get_selection_label(self, selection_value):
        # Lấy thông tin chi tiết về field selection
        selection_info = self.fields_get(allfields=['th_gender', 'th_profile_status', 'th_handover_status'])
        selection_values = (selection_info['th_gender']['selection'] +
                            selection_info['th_profile_status']['selection'] +
                            selection_info['th_handover_status']['selection'])

        # Tìm giá trị chuỗi tương ứng với giá trị key
        for key, label in selection_values:
            if key == selection_value:
                return label
        return None

    @api.onchange('th_handover_status')
    def onchange_th_date_of_delivery(self):
        for rec in self:
            if rec.th_handover_status == 'handed_over':
                rec.th_date_of_delivery = date.today()
                rec.th_check_handover = True
                rec.th_selected_handover = True
            else:
                rec.th_date_of_delivery = False
                rec.th_check_handover = False
                rec.th_selected_handover = False
                rec.th_handover_list_id = False

    @api.depends('th_handover_list_id')
    def get_handover_session_name(self):
        for rec in self:
            if rec.th_handover_list_id and rec.th_handover_status == 'handed_over':
                rec.th_handover_session = rec.th_handover_list_id.name
            else:
                rec.th_handover_session = None

from typing import Annotated

# from test4 import result
from ..schemas import StatusCategoryDatas, StatusDetailDatas, OwnershipUnitDatas, SourceGroupDatas, OriginDatas, \
GraduationSystemDatas, TrainingSystemDatas, ProductCategoryDatas, MajorDatas, AdmissionsStationDatas, AdmissionsRegionDatas, APMLevelDatas, \
ProductTemplateDatas,InfoChannelDatas,CountryDistrictDatas,CountryWardDatas,StatusStudentParticularDatas,StatusStudentFee,ExemptedSubjectData,CountryStateDatas,StudentProfileDatas,MailMessageDatas, \
CrmTagDatas
from ..schemas import PricelistDatas,PricelistItemDatas, RecordDatas,ResPartnerTitleDatas
from ..schemas import CrmLeadDatas, DividingRingDatas, CrmStageDatas, ResPartnerDatas
import requests
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path, Body
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError

router = APIRouter(tags=["crm"])


@router.post("/api/statuscategory")
def create_status_category(
    stt_data: StatusCategoryDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            # stt_category_data = {
            #     'name': stt_data.name,
            #     'th_type': stt_data.th_type,
            #     'th_description': stt_data.th_description,
            # }
            status_category = fastapi.env['th.status.category'].th_create_status_category(datas=stt_data)
            # if status_category:
            #     stt_details = []
            #     for rec in stt_data.th_status_detail_ids:
            #         stt_detail = fastapi.env['th.status.detail'].with_context(
            #             default_th_status_category_id=status_category.id
            #         ).th_create_status_detail(datas=rec)
            #         if stt_detail:
            #             stt_details.append({
            #                 "id": stt_detail.id,
            #                 "name": stt_detail.name,
            #             })
            #     results.append({
            #         "id": status_category.id,
            #         "status_details": stt_details
            #     })
            return {'id': status_category.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/statuscategory/{id}")
def write_status_category(
    stt_data: StatusCategoryDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            status_category = fastapi.env['th.status.category'].browse(id)
            if not status_category.exists():
                raise HTTPException(status_code=404, detail="Status Category not found.")
            data_to_update = stt_data.model_dump(exclude_none=True, exclude_unset=True)
            status_category.sudo().write(data_to_update)
            # for rec in stt_data.th_status_detail_ids:
            #     fastapi.env['th.status.detail'].with_context(default_th_status_category_id=status_category.id).th_create_status_detail(datas=rec.dict())
            return {
                'id': status_category.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/statuscategory/{id}")
def delete_status_category(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            status_category = fastapi.env['th.status.category'].browse(id)
            if not status_category.exists():
                raise HTTPException(status_code=404, detail="Status Category not found.")

            status_category.unlink()
            return {"detail": f"Status Category with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/statusdetail")
def create_status_detail(
    stt_detail_data: StatusDetailDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            status_category = fastapi.env['th.status.detail'].th_create_status_detail(datas=stt_detail_data)
            return {'id': status_category.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/statusdetail/{id}")
def write_status_detail(
    stt_detail_data: StatusDetailDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            status_detail = fastapi.env['th.status.detail'].browse(id)
            if not status_detail.exists():
                raise HTTPException(status_code=404, detail="Status Detail not found.")
            data_to_update = stt_detail_data.model_dump(exclude_unset=True)
            status_detail.write(data_to_update)
            return {
                'id': status_detail.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/statusdetail/{id}")
def delete_status_detail(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            status_detail = fastapi.env['th.status.detail'].browse(id)
            if not status_detail.exists():
                raise HTTPException(status_code=404, detail="Status Detail not found.")

            status_detail.unlink()
            return {"detail": f"Status Detail with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/ownershipunit")
def create_ownership(
    ownership_unit_data: OwnershipUnitDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            ownership_unit = fastapi.env['th.ownership.unit'].th_create_ownership_unit(datas=ownership_unit_data)
            return {'id': ownership_unit.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api/respartnertitle")
def create_ownership(
    res_partner_title_data: ResPartnerTitleDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            PartnerTitleDatas = fastapi.env['res.partner.title'].create(res_partner_title_data.dict())
            return {'id': PartnerTitleDatas.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/api/ownershipunit/{id}")
def write_status_detail(
    ownership_unit_data: OwnershipUnitDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            ownership_unit = fastapi.env['th.ownership.unit'].browse(id)
            if not ownership_unit.exists():
                raise HTTPException(status_code=404, detail="Ownership not found.")
            data_to_update = ownership_unit_data.model_dump(exclude_unset=True)
            ownership_unit.write(data_to_update)
            return {
                'id': ownership_unit.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/ownershipunit/{id}")
def delete_status_detail(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            status_detail = fastapi.env['th.ownership.unit'].browse(id)
            if not status_detail.exists():
                raise HTTPException(status_code=404, detail="Ownership not found.")

            status_detail.unlink()
            return {"detail": f"Ownership with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/sourcegroup")
def create_source_group(
    source_group_data: SourceGroupDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            source_group = fastapi.env['th.source.group'].sudo().with_context(th_sync=True).th_create_source_group(source_group_data.model_dump())
            return {
                'id': source_group.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/sourcegroup/{id}")
def write_source_group(
    source_group_data: SourceGroupDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            source_group = fastapi.env['th.source.group'].browse(id)
            if not source_group.exists():
                raise HTTPException(status_code=404, detail="Source Group not found.")
            data_to_update = source_group_data.model_dump(exclude_unset=True)
            source_group.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': source_group.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/sourcegroup/{id}")
def delete_source_group(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            source_group = fastapi.env['th.source.group'].browse(id)
            if not source_group.exists():
                raise HTTPException(status_code=404, detail="Source Group not found.")
            source_group.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/graduationsystem")
def create_graduation_system(
    graduation_system_data: GraduationSystemDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            graduation_system = fastapi.env['th.graduation.system'].sudo().with_context(th_sync=True).th_create_graduation_system(graduation_system_data.model_dump())
            return {
                'id': graduation_system.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/graduationsystem/{id}")
def write_graduation_system(
    graduation_system_data: GraduationSystemDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            graduation_system = fastapi.env['th.graduation.system'].browse(id)
            if not graduation_system.exists():
                raise HTTPException(status_code=404, detail="Graduation System not found.")
            data_to_update = graduation_system_data.model_dump(exclude_unset=True)
            graduation_system.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': graduation_system.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/graduationsystem/{id}")
def delete_graduation_system(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            graduation_system = fastapi.env['th.graduation.system'].browse(id)
            if not graduation_system.exists():
                raise HTTPException(status_code=404, detail="Graduation System not found.")
            graduation_system.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/productcategory")
def create_product_category(
    product_category_data: ProductCategoryDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            product_category = fastapi.env['product.category'].sudo().with_context(th_sync=True).create(product_category_data.model_dump(exclude_unset=True))
            return {
                'id': product_category.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/productcategory/{id}")
def write_product_category(
    product_category_data: ProductCategoryDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            product_category = fastapi.env['product.category'].browse(id)
            if not product_category.exists():
                raise HTTPException(status_code=404, detail="Product Category not found.")
            data_to_update = product_category_data.model_dump(exclude_unset=True)
            product_category.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': product_category.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/productcategory/{id}")
def delete_product_category(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            product_category = fastapi.env['product.category'].browse(id)
            if not product_category.exists():
                raise HTTPException(status_code=404, detail="Product Category not found.")

            product_category.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/major")
def create_major(
    major_data: MajorDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            major = fastapi.env['th.major'].sudo().with_context(th_sync=True).th_create_major(major_data.model_dump())
            return {
                'id': major.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/major/{id}")
def write_major(
    major_data: MajorDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            major = fastapi.env['th.major'].browse(id)
            if not major.exists():
                raise HTTPException(status_code=404, detail="Major not found.")
            data_to_update = major_data.model_dump(exclude_unset=True)
            major.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': major.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/major/{id}")
def delete_major(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            major = fastapi.env['th.major'].browse(id)
            if not major.exists():
                raise HTTPException(status_code=404, detail="Major not found.")

            major.sudo().with_context(th_sync=True).unlink()
            return {"detail": f"Major with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/admissionsstation")
def create_admissions_station(
    admissions_station_data: AdmissionsStationDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            admissions_station = fastapi.env['th.admissions.station'].sudo().with_context(th_sync=True).th_create_admissions_station(admissions_station_data.model_dump())
            return {
                'id': admissions_station.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/admissionsstation/{id}")
def write_admissions_station(
    admissions_station_data: AdmissionsStationDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            admissions_station = fastapi.env['th.admissions.station'].browse(id)
            if not admissions_station.exists():
                raise HTTPException(status_code=404, detail="Admissions Station not found.")
            data_to_update = admissions_station_data.model_dump(exclude_unset=True)
            admissions_station.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': admissions_station.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/admissionsstation/{id}")
def delete_admissions_station(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            admissions_station = fastapi.env['th.admissions.station'].browse(id)
            if not admissions_station.exists():
                raise HTTPException(status_code=404, detail="Admissions Station not found.")

            admissions_station.sudo().with_context(th_sync=True).unlink()
            return {"detail": f"Admissions Station with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))



@router.post("/api/admissionsregion")
def create_admissions_region(
    admissions_region_data: AdmissionsRegionDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            admissions_region = fastapi.env['th.admissions.region'].sudo().with_context(th_sync=True).th_create_admissions_region(admissions_region_data.model_dump())
            return {
                'id': admissions_region.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/admissionsregion/{id}")
def write_admissions_region(
    admissions_region_data: AdmissionsRegionDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            admissions_region = fastapi.env['th.admissions.region'].browse(id)
            if not admissions_region.exists():
                raise HTTPException(status_code=404, detail="Admissions Region not found.")
            data_to_update = admissions_region_data.model_dump(exclude_unset=True)
            admissions_region.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': admissions_region.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/admissionsregion/{id}")
def delete_admissions_region(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            admissions_region = fastapi.env['th.admissions.region'].browse(id)
            if not admissions_region.exists():
                raise HTTPException(status_code=404, detail="Admissions Region not found.")

            admissions_region.sudo().with_context(th_sync=True).unlink()
            return {"detail": f"Admissions Region with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/origin")
def create_origin(
        origin_data: OriginDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            data_for_origin = {
                'name': origin_data.name,
                'th_code': origin_data.th_code,
                'th_module_ids': origin_data.th_module_ids,
                'th_address': origin_data.th_address,
                'th_description': origin_data.th_description
            }
            origin = fastapi.env['th.origin'].th_create_origin(datas=data_for_origin)
            if origin:
                for rec in origin_data.th_university_major_ids:
                    university_major = fastapi.env['th.university.major'].with_context(default_th_origin_id=origin.id).th_create_university_major(datas=rec.dict())
            return {'id': origin.id}
    except UserError as e:
            raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/origin/{id}")
def write_origin(
    origin_data: OriginDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            origin = fastapi.env['th.origin'].browse(id)
            if not origin.exists():
                raise HTTPException(status_code=404, detail="Origin not found.")
            data_to_update = origin_data.model_dump(exclude_unset=True)
            data_for_origin = {
                'name': origin_data.name,
                'th_code': origin_data.th_code,
                'th_module_ids': origin_data.th_module_ids,
                'th_address': origin_data.th_address,
                'th_description': origin_data.th_description
            }
            origin.sudo().write(data_for_origin)
            origin.th_university_major_ids.unlink()
            for rec in origin_data.th_university_major_ids:
                fastapi.env['th.university.major'].with_context(default_th_origin_id=origin.id).th_create_university_major(datas=rec.dict())
            return {
                'id': origin.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/origin/{id}")
def delete_origin(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            origin = fastapi.env['th.origin'].browse(id)
            if not origin.exists():
                raise HTTPException(status_code=404, detail="Origin not found.")
            origin.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/apmlevel")
def create_apm_level(
    apm_level_data: APMLevelDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            apm_level = fastapi.env['th.apm.level'].sudo().with_context(th_sync=True).create(apm_level_data.model_dump(exclude_unset=True))
            return {
                'id': apm_level.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/apmlevel/{id}")
def write_apm_level(
    apm_level_data: APMLevelDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_level = fastapi.env['th.apm.level'].browse(id)
            if not apm_level.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy mối quan hệ.")
            data_to_update = apm_level_data.model_dump(exclude_unset=True)
            apm_level.sudo().with_context(th_sync=True).write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/apmlevel/{id}")
def delete_apm_level(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_level = fastapi.env['th.apm.level'].browse(id)
            if not apm_level.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy mối quan hệ.")
            apm_level.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/producttemplate")
def create_product_template(
    product_template_data: ProductTemplateDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            values = product_template_data.model_dump(exclude_none=True, exclude_unset=True)
            th_product_product_samp_ids = values.pop('th_product_product_samp_ids', None)
            product_template = fastapi.env['product.template'].sudo().with_context(th_sync=True).create(values)
            res = {
                'id': product_template.id,
            }
            if th_product_product_samp_ids:
                res['th_product_product_b2b_ids'] = product_template.product_variant_ids.ids
            return res
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/producttemplate/{id}")
def write_product_template(
    product_template_data: ProductTemplateDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            product_template = fastapi.env['product.template'].browse(id)
            if not product_template.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy sản phẩm.")
            data_to_update = product_template_data.model_dump(exclude_unset=True)
            data_to_update.pop('th_product_product_samp_ids', None)
            product_template.sudo().with_context(th_sync=True).write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/producttemplate/{id}")
def delete_product_template(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            product_template = fastapi.env['product.template'].browse(id)
            if not product_template.exists():
                raise HTTPException(status_code=404, detail="Product Template not found.")
            product_template.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api/crmlead")
def create_crm_lead(
    crm_lead_data: CrmLeadDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            values = crm_lead_data.model_dump(exclude_none=True, exclude_unset=True)
            partner_info = values.pop('partner_info', None)
            result = {}
            if not values['partner_id'] and partner_info:
                partner = fastapi.env['res.partner'].create(partner_info)
                values['partner_id'] = partner.id
                result['partner_id'] = partner.id
            crm_lead = fastapi.env['crm.lead'].sudo().with_context(th_sync=True).create(values)
            result['id'] = crm_lead.id
            return result

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/crmlead/{id}")
def update_crm_lead(
    crm_lead_data: CrmLeadDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            update_data_crm(crm_lead_data, fastapi, id)
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def update_data_crm(
    crm_lead_data: CrmLeadDatas,
    fastapi,
    id: int = Path(..., ge=1)
):
    crm_lead = fastapi.env['crm.lead'].browse(id)
    if not crm_lead.exists():
        raise HTTPException(status_code=404, detail="Không tìm thấy cơ hội.")
    data_to_update = crm_lead_data.model_dump(exclude_none=True, exclude_unset=True)
    partner_info = data_to_update.pop('partner_info', None)
    if all(key in data_to_update for key in
           ["th_duplicate_processed_lead", "th_is_a_duplicate_opportunity", "th_duplicate_type",
            "th_crm_lead_arbitrate_lose"]):
        crm_lead_arbitrate_lose = fastapi.env['crm.lead'].search(
            [('name', '=', data_to_update.get('th_crm_lead_arbitrate_lose', False))], limit=1)
        if crm_lead_arbitrate_lose:
            crm_lead_arbitrate_lose.sudo().write({
                'th_is_a_duplicate_opportunity': True,
                'th_duplicate_type': 'manual',
                'th_duplicate_processed_lead': True,
            })
        data_to_update.pop("th_crm_lead_arbitrate_lose", None)
    crm_lead.sudo().with_context(th_sync=True).write(data_to_update)


@router.post("/api/updatecrm")
async def sync_crm(
    records: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            results = []
            for record in records:
                try:
                    response = update_data_crm(record.th_data_crm, fastapi, record.id_b2b)
                    results.append({
                        "status": "success",
                        "response": response,
                    })
                except Exception as e:
                    results.append({
                        "status": "error",
                        "response": str(e),
                    })
            return results
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/crmlead/{id}")
def delete_crm_lead(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            crm_lead = fastapi.env['crm.lead'].browse(id)
            if not crm_lead.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy cơ hội.")
            crm_lead.sudo().with_context(th_sync=True).unlink()
            return {"status": "success", "message": "Xóa thành công các bản ghi CRM."}

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/infochannel")
def create_info_chanel(
    info_channel_data: InfoChannelDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            info_channel = fastapi.env['th.info.channel'].th_create_channel(info_channel_data.model_dump())
            return {
                'id': info_channel.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/infochannel/{id}")
def write_info_chanel(
    info_channel_data: InfoChannelDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            info_channel = fastapi.env['th.info.channel'].browse(id)
            if not info_channel.exists():
                raise HTTPException(status_code=404, detail="Channel not found.")
            data_to_update = info_channel_data.model_dump(exclude_unset=True)
            info_channel.write(data_to_update)
            return {
                'id': info_channel.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/infochannel/{id}")
def delete_info_chanel(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            info_channel = fastapi.env['th.info.channel'].browse(id)
            if not info_channel.exists():
                raise HTTPException(status_code=404,
                                    detail=f"Kênh không tồn tại")
            info_channel.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/countrydistrict")
def create_country_district(
    countrydata: CountryDistrictDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            country_district = fastapi.env['th.country.district'].th_create_district(countrydata.model_dump())
            return {
                'id': country_district.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/countrydistrict/{id}")
def write_country_district(
        countrydata: CountryDistrictDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            country_district = fastapi.env['th.country.district'].browse(id)
            if not country_district.exists():
                raise HTTPException(status_code=404, detail="Country District not found.")
            data_to_update = countrydata.model_dump(exclude_unset=True)
            country_district.write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/countrydistrict/{id}")
def delete_country_district(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            country_district = fastapi.env['th.country.district'].browse(id)
            if  not country_district.exists():
                raise HTTPException(status_code=404,detail=f"Quận/Huyện không tồn tại")
            country_district.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/countryward")
def create_country_district(
    country_ward_data: CountryWardDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            country_ward = fastapi.env['th.country.ward'].th_create_ward(country_ward_data.model_dump())
            return {
                'id': country_ward.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/countryward/{id}")
def write_country_ward(
        country_ward_data: CountryWardDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            country_ward = fastapi.env['th.country.ward'].browse(id)
            if not country_ward.exists():
                raise HTTPException(status_code=404, detail="Country ward not found.")
            data_to_update = country_ward_data.model_dump(exclude_unset=True)
            country_ward.write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/countryward/{id}")
def delete_country_ward(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            country_ward = fastapi.env['th.country.ward'].browse(id)
            if not country_ward.exists():
                raise HTTPException(status_code=404,
                                    detail=f"Phường/xã không tồn tại")
            country_ward.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/statusstudentparticular")
def create_status_student_particular(
    stt_data: StatusStudentParticularDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            th_status_student_particular = fastapi.env['th.status.student.particular'].th_create_status_student_particular(data=stt_data.model_dump(exclude_none=True, exclude_unset=True))
            return {'id': th_status_student_particular.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/statusstudentparticular/{id}")
def th_write_status_student_particular(
    stt_data: StatusStudentParticularDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            stt_id = fastapi.env['th.status.student.particular'].browse(id)
            if not stt_id:
                raise HTTPException(status_code=404, detail="Không tồn tại trạng thái chi tiết sinh viên này")
            stt_id.write(dict(stt_data.model_dump(exclude_none=True, exclude_unset=True)))
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/statusstudentparticular/{id}")
def delete_status_student_particular(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            particular = fastapi.env['th.status.student.particular'].browse(id)
            if not particular.exists():
                raise HTTPException(status_code=404,
                                    detail=f"Chi tiết tình trạng sinh viên")
            particular.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/statusstudentfee")
def th_create_status_student_fee(
    stt_data: StatusStudentFee,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            th_fee_status = fastapi.env['th.fee.status.particular'].th_create_fee_status(data=stt_data)
            return {'id': th_fee_status.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/statusstudentfee/{id}")
def th_write_status_student_fee(
    stt_data: StatusStudentFee,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            stt_id = fastapi.env['th.fee.status.particular'].browse(id)
            if not stt_id:
                raise HTTPException(status_code=404, detail="Không tồn tại trạng thái chi tiết học phí này")
            stt_id.write(dict(stt_data.model_dump(exclude_none=True, exclude_unset=True)))
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/statusstudentfee/{id}")
def delete_status_student_fee(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            fee = fastapi.env['th.fee.status.particular'].browse(id)
            if not fee.exists():
                raise HTTPException(status_code=404,
                                    detail=f"Chi tiết tình trạng học phí")
            fee.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/exemptedsubject")
def create_exempted_subject(
    exempted_subject: ExemptedSubjectData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            exempted_Subject = fastapi.env['th.exempted.subject'].th_create_exempted_subject(datas=exempted_subject)
            return {'id': exempted_Subject.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/exemptedsubject/{id}")
def write_exempted_subject(
    exempted_subject: ExemptedSubjectData,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            exempted_Subject = fastapi.env['th.exempted.subject'].browse(id)
            if not exempted_Subject.exists():
                raise HTTPException(status_code=404, detail="Exempted Subject not found.")
            data_to_update = exempted_subject.model_dump(exclude_none=True, exclude_unset=True)
            exempted_Subject.write(data_to_update)
            return {
                'id': exempted_Subject.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/exemptedsubject/{id}")
def delete_exempted_subject(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            exempted_Subject = fastapi.env['th.exempted.subject'].browse(id)
            if not exempted_Subject.exists():
                raise HTTPException(status_code=404, detail="Exempted Subject not found.")

            exempted_Subject.unlink()
            return {"detail": f"Exempted Subject with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/countrystate")
def create_country_state(
    CountryStateDatas: CountryStateDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            country_state = fastapi.env['res.country.state'].th_create_state(CountryStateDatas.model_dump())
            return {
                'id': country_state.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/countrystate/{id}")
def write_country_state(
        CountryStateDatas: CountryStateDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            country_state = fastapi.env['res.country.state'].browse(id)
            if not country_state.exists():
                raise HTTPException(status_code=404, detail="Country ward not found.")
            data_to_update = CountryStateDatas.model_dump(exclude_none=True, exclude_unset=True)
            if data_to_update.get('country_id'):
                data_to_update['country_id'] = fastapi.env['res.country'].search([('code', '=', data_to_update.get('country_id'))]).id
            country_state.write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/countrystate/{id}")
def delete_country_state(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            country_state = fastapi.env['res.country.state'].browse(id)
            if not country_state.exists():
                raise HTTPException(status_code=404,
                                    detail=f"Tỉnh không tồn tại")
            country_state.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/studentprofile")
def create_student_profile(
    student_profile_data: StudentProfileDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            student_profile = fastapi.env['th.student.profile'].th_create_student_profile(datas=student_profile_data.dict())
            return {'id': student_profile.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/studentprofile/{id}")
def write_student_profile(
    student_profile_data: StudentProfileDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            student_profile = fastapi.env['th.student.profile'].browse(id)
            if not student_profile.exists():
                raise HTTPException(status_code=404, detail="Student profile not found.")
            data_to_update = student_profile_data.model_dump(exclude_none=True, exclude_unset=True)
            student_profile.write(data_to_update)
            return {
                'id': student_profile.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/studentprofile/{id}")
def delete_student_profile(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            student_profile = fastapi.env['th.student.profile'].browse(id)
            if not student_profile.exists():
                raise HTTPException(status_code=404,
                                    detail=f"Hồ sơ không tồn tại")
            student_profile.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/mailmessages")
def create_mail(
    mail_data: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            responses = []
            for rec in mail_data:
               values = rec.model_dump()
               if rec.id_b2b:
                   response = update_mail_message(rec.th_data_mail, fastapi, values['id_b2b'])
                   responses.append({
                       'id': values.get('id_b2b', False),
                   })
               else:
                   response = create_mail_message(rec.th_data_mail, fastapi)
                   responses.append(response)
            return responses
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def create_mail_message(
    MailMessageDatas: MailMessageDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            mail_message = fastapi.env['mail.message'].with_context(th_sync=True).th_create_mail_message(datas=MailMessageDatas.dict())
            return {'id': mail_message.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def update_mail_message(
        MailMessageDatas: MailMessageDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            MessageDatas = fastapi.env['mail.message'].browse(id)
            if not MessageDatas.exists():
                raise HTTPException(status_code=404, detail="Không tìm log note")
            data_to_update = MailMessageDatas.model_dump(exclude_none=True, exclude_unset=True)
            MessageDatas.sudo().with_context(th_sync=True).write(data_to_update)
            return

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/mailmessage/{id}")
def delete_mail_message(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            fee = fastapi.env['mail.message'].browse(id)
            if not fee.exists():
                raise HTTPException(status_code=404,
                                    detail=f"log note")
            fee.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/dividingring")
def create_dividing_ring(
    dividing_ring_data: DividingRingDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            values = dividing_ring_data.model_dump(exclude_none=True, exclude_unset=True)
            dividing_ring = fastapi.env['th.dividing.ring'].sudo().with_context(th_sync=True).create(values)
            return {'id': dividing_ring.id,}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/dividingring/{id}")
def write_dividing_ring(
    dividing_ring_data: DividingRingDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            dividing_ring = fastapi.env['th.dividing.ring'].browse(id)
            if not dividing_ring.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy vòng chia.")
            data_to_update = dividing_ring_data.model_dump(exclude_unset=True)
            dividing_ring.sudo().with_context(th_sync=True).write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/dividingring/{id}")
def delete_dividing_ring(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            dividing_ring = fastapi.env['th.dividing.ring'].browse(id)
            if not dividing_ring.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy vòng chia.")
            dividing_ring.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/crmstage")
def create_crm_stage(
    crm_stage_data: CrmStageDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            values = crm_stage_data.model_dump(exclude_none=True, exclude_unset=True)
            crm_stage = fastapi.env['crm.stage'].sudo().with_context(th_sync=True).create(values)
            return {'id': crm_stage.id,}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/crmstage/{id}")
def write_crm_stage(
    crm_stage_data: CrmStageDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            crm_stage = fastapi.env['crm.stage'].browse(id)
            if not crm_stage.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy mối quan hệ crm.stage.")
            data_to_update = crm_stage_data.model_dump(exclude_unset=True)
            crm_stage.sudo().with_context(th_sync=True).write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/crmstage/{id}")
def delete_crm_stage(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            crm_stage = fastapi.env['crm.stage'].browse(id)
            if not crm_stage.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy mối quan hệ crm.stage.")
            crm_stage.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/contact")
def call_contact(
    contact_data: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
):
    try:
        if fastapi:
            data =[]
            for rec in contact_data:
                if rec:
                    record =update_contact(rec, fastapi, id)
                    data.append(record)
            return data
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/api/contact/{id}")
def write_contact(
    contact_data: ResPartnerDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            return update_contact(contact_data, fastapi, id)
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def update_contact(
    contact_data: ResPartnerDatas,
    fastapi,
    id: int = Path(..., ge=0)
):
    contact = fastapi.env['res.partner'].browse(contact_data.id_b2b)
    if not contact.exists():
        raise HTTPException(status_code=404, detail="Không tìm thấy liên hệ.")
    data_to_update = contact_data.th_data_res_partner.model_dump(exclude_none=True, exclude_unset=True)
    if data_to_update.get("country_id",False):
        country = fastapi.env["res.country"].sudo().search(
            [("code", "=", data_to_update.get("country_id",False))], limit=1
        )
        data_to_update["country_id"] = country.id

    if data_to_update.get("th_country_id",False):
        country = fastapi.env["res.country"].sudo().search(
            [("code", "=", data_to_update.get("th_country_id", False))], limit=1
        )
        data_to_update["th_country_id"] = country.id
    data_for_contact = data_to_update.copy()
    # if data_to_update.get('th_apm_contact_trait_ids', False):
    #     del data_for_contact["th_apm_contact_trait_ids"]
    #     contact.th_apm_contact_trait_ids.unlink()
    #     for rec in data_to_update['th_apm_contact_trait_ids']:
    #         apm_trait_value = fastapi.env['th.apm.contact.trait'].with_context(default_th_partner_id=contact.id).create(rec)
    contact.sudo().with_context(th_sync=True).write(data_for_contact)
    contact.sudo().with_context(th_sync=True).write(data_to_update)

    return {
        "id": contact.id,
        'response': 'Liên hệ đã được cập nhật thành công.'
    }


@router.post("/api/pricelist/")
def create_pricelist(
    pricelist_data: PricelistDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            values = pricelist_data.model_dump(exclude_none=True, exclude_unset=True)
            items = values.pop('items', [])
            pricelist = fastapi.env['product.pricelist'].sudo().with_context(th_sync=True).create(values)
            result = {
                'id': pricelist.id
            }
            if len(items) > 0:
                item_ids = []
                for item in items:
                    item['pricelist_id'] = pricelist.id
                    pricelist_item = fastapi.env['product.pricelist.item'].sudo().with_context(th_sync=True).create(item)
                    item_ids.append(pricelist_item.id)
                result['items'] = item_ids
            return result
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/pricelist/{id}")
def write_pricelist(
    pricelist_data: PricelistDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            pricelist = fastapi.env['product.pricelist'].browse(id)
            if not pricelist.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy bảng giá.")
            data_to_update = pricelist_data.model_dump(exclude_none=True, exclude_unset=True)
            items = data_to_update.pop('items', [])
            pricelist.sudo().with_context(th_sync=True).write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/pricelist/{id}")
def delete_pricelist(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            pricelist = fastapi.env['product.pricelist'].browse(id)
            if not pricelist.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy bảng giá.")
            pricelist.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/pricelistitem/")
def create_pricelist_item(
    pricelist_item_data: PricelistItemDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            pricelist_item = fastapi.env['product.pricelist.item'].sudo().with_context(th_sync=True).create(pricelist_item_data.model_dump())
            return {
                'id': pricelist_item.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/pricelistitem/{id}")
def write_pricelist(
    pricelist_item_data: PricelistItemDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            pricelist_item = fastapi.env['product.pricelist.item'].browse(id)
            if not pricelist_item.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy quy tắc bảng giá.")
            data_to_update = pricelist_item_data.model_dump(exclude_none=True, exclude_unset=True)
            pricelist_item.sudo().with_context(th_sync=True).write(data_to_update)
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/pricelistitem/{id}")
def delete_pricelist(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            pricelist_item = fastapi.env['product.pricelist.item'].browse(id)
            if not pricelist_item.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy quy tắc bảng giá.")
            pricelist_item.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/trainingsystem")
def th_create_training_system(
    training_system_data: TrainingSystemDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            training_system = fastapi.env['th.training.system'].sudo().with_context(th_sync=True).create(training_system_data.model_dump(exclude_unset=True))
            return {
                'id': training_system.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/trainingsystem/{id}")
def th_write_training_system(
    training_system_data: TrainingSystemDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            training_system = fastapi.env['th.training.system'].browse(id)
            if not training_system.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy hệ đào tạo.")
            data_to_update = training_system_data.model_dump(exclude_unset=True)
            training_system.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': training_system.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/trainingsystem/{id}")
def th_delete_training_system(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            training_system = fastapi.env['th.training.system'].browse(id)
            if not training_system.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy hệ đào tạo.")
            training_system.sudo().with_context(th_sync=True).unlink()
            return {"detail": f"Hệ đào tạo với id {id} đã được xóa."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api/crmstag")
def th_create_crm_tag(
    crm_tag_data: CrmTagDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            crm_tag = fastapi.env['crm.tag'].sudo().with_context(th_sync=True).create(crm_tag_data.model_dump(exclude_unset=True))
            return {
                'id': crm_tag.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

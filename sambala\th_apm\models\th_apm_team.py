from odoo import _, fields, models, api, exceptions, Command
from odoo.exceptions import ValidationError
import json

class APMTeam(models.Model):
    _name = "th.apm.team"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Đội ngũ"
    _order = "name"
    _rec_name = 'complete_name'
    _parent_store = True


    def _th_domain_manager(self):
        users = self.env.ref('th_apm.group_apm_leader').users.ids
        return [('id', 'in', users)]

    name = fields.Char('Tên đội/nhóm', required=True)
    complete_name = fields.Char('Tên hoàn thành', compute='_compute_complete_name', recursive=True, store=True)
    active = fields.Boolean('Active', default=True)
    parent_id = fields.Many2one('th.apm.team', string='Đội/nhóm cha', index=True , domain="[('id', '!=', id)]")
    child_ids = fields.One2many('th.apm.team', 'parent_id', string='Đội/nhóm con')
    total_member = fields.Integer(compute='_compute_total_member', string='Tổng số thành viên')
    color = fields.Integer('Màu')
    parent_path = fields.Char(index=True, unaccent=False)
    master_team_id = fields.Many2one(
        'th.apm.team', string='Đội chính', compute='_compute_master_team_id', store=True)
    manager_id = fields.Many2one(comodel_name="res.users", string="Quản lý", tracking=True, domain=_th_domain_manager)
    th_description = fields.Text(string="Mô tả")
    th_member_ids = fields.Many2many(comodel_name="res.users", string="Thành viên" )
    parent_member_ids = fields.Char(string="Thành viên của đội cha", compute="_compute_th_member_ids")
    th_flag = fields.Char(string="Cờ")
    th_lead_id = fields.Many2one("th.apm")
    th_is_ttvh = fields.Boolean(string="Là trung tâm vận hành", default=False)
    th_leader_ids = fields.Many2many(comodel_name="res.users", relation="apm_team_leader_rel",
                                     string="Danh sách quản lý", compute='_compute_th_leader_ids', store=True,
                                     recursive=True)

    def _compute_total_member(self):
        member_data = self.env['res.users']._read_group([('th_apm_team_id', 'in', self.ids)], ['th_apm_team_id'], ['th_apm_team_id'])
        result = dict((data['th_apm_team_id'][0], data['th_apm_team_id']) for data in member_data)
        for team in self:
            team.total_member = result.get(team.id, 0)

    @api.depends('manager_id', 'parent_id', 'parent_id.th_leader_ids')
    def _compute_th_leader_ids(self):
        for rec in self:
            rec.th_leader_ids = False
            if rec.parent_id:
                rec.th_leader_ids = [Command.link(leader_id.id) for leader_id in rec.parent_id.th_leader_ids] + [
                    Command.link(rec.manager_id.id)] if rec.parent_id and rec.manager_id else [
                    Command.set([rec.manager_id.id])] if rec.manager_id else False
    @api.depends('parent_id', 'manager_id')
    def _compute_th_member_ids(self):
        for rec in self:
            domain = []
            if rec.manager_id:
                domain.append(('id', '!=', rec.manager_id.id))
            if rec.parent_id:
                member_ids = rec.parent_id.th_member_ids
                domain.append(('id', 'in', member_ids.ids))
            rec.parent_member_ids = json.dumps(domain)

    @api.depends('name', 'parent_id.complete_name')
    def _compute_complete_name(self):
        for team in self:
            if team.parent_id:
                team.complete_name = '%s / %s' % (team.parent_id.complete_name, team.name)
            else:
                team.complete_name = team.name

    @api.depends('parent_path')
    def _compute_master_team_id(self):
        for team in self:
            team.master_team_id = int(team.parent_path.split('/')[0])

    def name_get(self):
        if not self.env.context.get('hierarchical_naming', True):
            return [(record.id, record.name) for record in self]
        return super(APMTeam, self).name_get()

    @api.model
    def name_create(self, name):
        return self.create({'name': name}).name_get()[0]

    @api.constrains('parent_id')
    def _check_parent_id(self):
        if not self._check_recursion():
            raise ValidationError(_('Bạn không thể tạo phòng ban cha là chính nó!'))

    def get_children_team_ids(self):
        return self.env['th.apm'].search([('id', 'child_of', self.ids)])

    def change_values_team_apm(self, old_user_ids, new_user_ids):
        model = self.env['res.users']
        if old_user_ids:
            for rec in model.search([('id', 'in', [id for id in old_user_ids if id not in new_user_ids])]):
                rec.partner_id.write({'th_apm_team_id': False})
                team_ids = [x for x in self.parent_path.split('/') if x]
                if team_ids:
                    rec.partner_id.write({'th_apm_team_id': int(team_ids[-2]) if len(team_ids) > 1 else False})
        model.search([('id', 'in', new_user_ids)]).mapped('partner_id').write({'th_apm_team_id': self.id})

    def check_member_team_apm(self, member_ids):
        if any(user in self.search([]).mapped('th_member_ids').ids for user in member_ids):
            raise ValidationError(_('T'))

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        flag = 0
        for team in res:
            (team.th_member_ids + team.manager_id).write(
                {'th_manager_apm_ids': [(4, man.id) for man in team.th_leader_ids + team.manager_id]})
            team.th_flag = json.dumps({'unknown': [flag, False]})
            team.change_values_team_apm(False, team.th_member_ids.ids+team.manager_id.ids)
        return res

    def write(self, vals):
        for rec in self:
            manager_id = rec.manager_id
            th_member_ids = rec.th_member_ids
            old_user_ids = rec.th_member_ids.ids + rec.manager_id.ids
        res = super().write(vals)
        if vals.get('th_member_ids'):
            members_ids = self.th_member_ids.ids
            members_ids.sort()
            th_flag_dict = json.loads(self.th_flag) if self.th_flag else {'unknown': [0, False]}
            for th_flag in th_flag_dict:
                member_id = th_flag_dict[f'{th_flag}'][1] if th_flag_dict else False
                if not member_id:
                    continue
                if member_id in members_ids:
                    th_flag_dict[f'{th_flag}'] = [members_ids.index(member_id), member_id]
                    continue
                if len(members_ids) == 0 or len(members_ids) < member_id:
                    flag = 0
                    th_flag_dict[f'{th_flag}'] = [flag, False]
                    continue
                for index, member in enumerate(members_ids):
                    if member > member_id:
                        th_flag_dict[f'{th_flag}'] = [index, member]
                        break
            self.th_flag = json.dumps(th_flag_dict)
            for rec in self:
                rec.change_values_team_apm(old_user_ids, rec.th_member_ids.ids + rec.manager_id.ids)
        if 'th_member_ids' in vals or ' ' in vals:
            for rec in self:
                rec.th_member_ids.write({'th_manager_apm_ids': [(4, man.id) for man in rec.th_leader_ids + rec.manager_id]})
                rec.manager_id.write({'th_manager_apm_ids': [(4, man.id) for man in rec.th_leader_ids]})
                if rec.manager_id != manager_id:
                    rec.th_member_ids.write({'th_manager_apm_ids': [(3, manager_id.id)] + [(4, man.id) for man in rec.th_leader_ids + rec.manager_id]})
                if (th_member_ids - rec.th_member_ids):
                    (th_member_ids - rec.th_member_ids).write({'th_manager_apm_ids': [(3, manager_id.id)]})
        return res

    def unlink(self):
        res = super().unlink()
        for rec in res:
            rec.manager_id.partner_id.th_apm_team_id = False
            rec.th_member_ids.partner_id.th_apm_team_id = False
            (rec.th_member_ids + rec.manager_id).write(
                {'th_manager_apm_ids': [(3, man.id) for man in rec.th_leader_ids + rec.manager_id]})

    def action_assign_leads(self):
        for rec in self:
            if rec._context.get('division_type') == 'automatic':
                th_lead_id = False
                result = dict.fromkeys(self.ids, self.env['res.users'])
                member_ids = self.th_member_ids.ids
                member_ids.sort()
                if len(member_ids) == 0:
                    raise exceptions.UserError(
                        _("Đội/Nhóm '%s' chưa có thành viên. Vui lòng thêm thành viên vào nhóm để thực hiện chức năng này",
                          self.name))
                flag = 0
                th_flag = json.loads(self.th_flag) if self.th_flag else {'unknown': [0, False]}
                flag = th_flag.get('unknown')[0]
                result[self.id] = self.env['res.users'].browse(member_ids[flag])
                flag = (flag + 1) % len(member_ids)
                th_flag['unknown'] = [flag, member_ids[flag]]
                self.th_flag = json.dumps(th_flag)
                return result
            elif rec._context.get('division_type') == 'handmade':
                result = dict.fromkeys(self.ids, self.env['res.users'])
                result[self.id] = self.manager_id
                return result
            else:
                result = dict.fromkeys(self.ids, self.env['res.users'])
                result[self.id] = False
                return result

            
class ThApmDuplicateCheckHistory(models.Model):
    _name = "th.apm.duplicate.check.history"
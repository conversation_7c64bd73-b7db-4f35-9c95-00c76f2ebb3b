<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="th_feedback_ticket_form_view" model="ir.ui.view">
        <field name="name">th.feedback.ticket.form.view</field>
        <field name="model">th.feedback.ticket</field>
        <field name="arch" type="xml">
            <form string="Phiếu Feedback">
                <field name="is_no_error" invisible="1"/>
                <field name="th_ticket_stage" invisible="1"/>
                <field name="th_user_auto_assign_ids" invisible="1"/>
                <field name="th_team_ids" invisible="1"/>
                <header>
                    <button name="button_add_transferring_feedback"
                            string="Chuyển giao phiếu"
                            type="object" class="oe_highlight"
                            attrs="{'invisible': ['|', ('th_ticket_stage', 'in', ['new', 'cancel', 'error_classify', '']), ('id', '=', False)]}"
                            groups="th_feedback.group_feedback_category_manager"/>
                    <field name="th_stage_id" widget="statusbar" options="{'clickable': '0'}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" class="field_name" placeholder="Nhập tên phiếu feedback..."/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="th_partner_id" options="{'no_open': True, 'no_create': True}"/>
                            <field name="th_email"/>
                            <field name="th_phone"/>
                            <field name="th_deadline"/>
                            <field name="uid_create" options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_partner_ids" widget="many2many_tags" options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_attachment_ids" widget="many2many_binary"/>
                            <field name="priority" widget="priority"/>
                            <field name="th_sla_policy_id" options="{'no_open': True, 'no_create': True}"/>
                        </group>
                        <group>
                            <field name="th_category_id" options="{'no_open': True, 'no_create': True}"/>
                            <field name="th_topic_id" options="{'no_open': True, 'no_create': True}"/>
                            <field name="th_team_id" options="{'no_open': True, 'no_create': True}" groups="th_feedback.group_feedback_handler"/>
                            <field name="th_assign_id" options="{'no_open': True, 'no_create': True}" groups="th_feedback.group_feedback_handler"/>
                            <field name="th_deadline_expected"/>
                            <field name="th_partner_code" widget="many2one_dropdown"/>
                            <field name="th_study_name" widget="many2one_dropdown"/>
                            <field name="th_study_code"/>
                            <field name="th_component_position" options="{'no_open': True, 'no_create': True}"/>
                            <field name="th_question_position"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description" placeholder="Mô tả phiếu feedback..."/>
                        </page>
                        <page string="Phân loại lỗi"
                              attrs="{'invisible': ['|', ('th_ticket_stage', 'in', ('new', 'cancel')), ('id', '=', False)]}"
                              groups="th_feedback.group_feedback_handler">
                            <group>
                                <field name="th_error_id"
                                       options="{'no_open': True, 'no_create': True}"/>
                                <field name="th_error_detail_id"
                                       attrs="{'invisible': [('is_no_error', '=', True)]}"
                                       options="{'no_open': True, 'no_create': True}"/>
                                <field name="th_description_error"
                                       attrs="{'invisible': [('is_no_error', '!=', True)]}"/>
                            </group>
                        </page>
                        <page string="Chuyển giao" attrs="{'invisible': [('th_transfer_ids', '=', [])]}">
                            <field name="th_transfer_ids" create="false" delete="false" readonly="1">
                                <tree create="false" delete="false">
                                    <field name="th_user_transfer_id"/>
                                    <field name="th_assign_id"/>
                                    <field name="th_transfer_date"/>
                                    <field name="th_reason"/>
                                </tree>
                                <form string="Phiếu chuyển phiếu feedback" create="false" delete="false">
                                    <sheet>
                                        <group>
                                            <field name="th_user_auto_assign_ids" invisible="1"/>
                                            <field name="th_transfer_date" />
                                            <field name="th_reason" />
                                            <field name="th_category_id" options="{'no_open': True, 'no_create': True}"/>
                                            <field name="th_topic_id" options="{'no_open': True, 'no_create': True}"/>
                                            <field name="th_team_id" options="{'no_open': True, 'no_create': True}"/>
                                            <field name="th_assign_id" options="{'no_open': True, 'no_create': True}"/>
                                        </group>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>


    <record id="th_feedback_ticket_tree_view" model="ir.ui.view">
        <field name="name">th.feedback.ticket.tree.view</field>
        <field name="model">th.feedback.ticket</field>
        <field name="arch" type="xml">
            <tree>
                <header>
                    <button name="th_action_merge_tickets"
                            string="Gom lỗi"
                            class="oe_highlight"
                            type="object"
                            confirm="Bạn có chắc chắn muốn gom các lỗi/feedback này?"/>
                </header>
                <field name="name"/>
                <field name="th_team_id" groups="th_feedback.group_feedback_handler"/>
                <field name="th_assign_id" groups="th_feedback.group_feedback_handler"/>
                <field name="th_stage_id"/>
                <field name="th_deadline_expected"/>
                <field name="th_company_id"/>
                <field name="th_partner_id"/>
                <field name="th_email"/>
                <field name="th_phone"/>
                <field name="th_deadline" optional="hide"/>
                <field name="uid_create" optional="hide"/>
                <field name="priority" optional="hide"/>
                <field name="th_sla_policy_id" optional="hide"/>
                <field name="th_category_id" optional="hide"/>
                <field name="th_topic_id" optional="hide"/>
                <field name="th_error_id" optional="hide"/>
                <field name="th_error_detail_id" optional="hide"/>
                <field name="th_description" optional="hide"/>
                <field name="th_attachment_ids" optional="hide"/>

            </tree>
        </field>
    </record>

    <record id="th_feedback_ticket_view_kanban" model="ir.ui.view">
        <field name="name">th.feedback.ticket.view.kanban</field>
        <field name="model">th.feedback.ticket</field>
        <field name="arch" type="xml">
            <kanban default_group_by="th_stage_id" class="o_kanban_small_column" quick_create_view="th_feedback.th_feedback_ticket_form_view" group_create="0" records_draggable="context.get('uid') in [user.id for user in user.env.ref('th_feedback.group_feedback_category_manager').users] or context.get('uid') in [user.id for user in user.env.ref('th_feedback.group_feedback_admin').users]" sample="1">
                <field name="th_stage_id" options="{'group_by_tooltip': {'description': 'Stage Description'}}" />
                <field name="th_assign_id"/>
                <field name="color"/>
                <field name="priority"/>
                <field name="th_team_id"/>
                <field name="th_partner_id"/>
                <field name="th_deadline_expected"/>
                <field name="th_category_id"/>
                <field name="th_topic_id"/>
                <field name="activity_ids"/>
                <field name="activity_state"/>
                <field name="th_ticket_stage"/>
                <!-- <progressbar field="kanban_state" colors="{&quot;done&quot;: &quot;success&quot;, &quot;blocked&quot;: &quot;danger&quot;, &quot;normal&quot;: &quot;200&quot;}"/> -->
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''} oe_kanban_global_click">
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.editable">
                                        <a type="edit" class="dropdown-item">Sửa</a>
                                    </t>
                                    <t t-if="widget.deletable">
                                        <a type="delete" class="dropdown-item">Xóa</a>
                                    </t>
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_title mb4">
                                    <strong>
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="mb4" t-if="record.th_partner_id.value">
                                    <field name="th_partner_id"/>
                                </div>
                                <div class="mb4 fs-6">
                                    <span t-if="record.th_ticket_stage.raw_value != 'new' and record.th_ticket_stage.raw_value != 'error_classify'">
                                        <field name="th_error_id"/>

                                    </span>
                                </div>

                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="priority" widget="priority"/>
                                        <field name="activity_ids" widget="kanban_activity"/>
                                        <b t-if="record.use_rating.raw_value and record.rating_count.raw_value &gt; 0" groups="helpdesk.group_use_rating">
                                            <strong class="fa fa-fw mt4 fa-smile-o text-success fw-bolder" t-if="record.rating_avg.raw_value &gt;= 3.66" title="Average Rating: Satisfied" role="img" aria-label="Happy face"/>
                                            <strong class="fa fa-fw mt4 fa-meh-o text-warning fw-bolder" t-elif="record.rating_avg.raw_value &gt;= 2.33" title="Average Rating: Okay" role="img" aria-label="Neutral face"/>
                                            <strong class="fa fa-fw mt4 fa-frown-o text-danger fw-bolder" t-else="" title="Average Rating: Dissatisfied" role="img" aria-label="Sad face"/>
                                        </b>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <field name="kanban_state" widget="state_selection" groups="base.group_user"/>
                                        <field t-if="record.th_assign_id.raw_value" name="th_assign_id" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="th_feedback_ticket_search_view" model="ir.ui.view">
        <field name="name">th.feedback.ticket.search</field>
        <field name="model">th.feedback.ticket</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm phiếu feedback">
                <field name="name"/>
                <field name="th_partner_id"/>
                <field name="th_category_id"/>
                <field name="th_topic_id"/>
                <field name="th_team_id"/>
                <field name="th_assign_id"/>
                <separator/>
                <filter string="Của tôi" name="my_tickets" domain="[('th_assign_id', '=', uid)]"/>
                <filter string="Chưa phân công" name="unassigned" domain="[('th_assign_id', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Giai đoạn" name="stage" context="{'group_by': 'th_stage_id'}"/>
                    <filter string="Danh mục" name="category" context="{'group_by': 'th_category_id'}"/>
                    <filter string="Đội" name="team" context="{'group_by': 'th_team_id'}"/>
                    <filter string="Người được phân công" name="assigned" context="{'group_by': 'th_assign_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="th_feedback_ticket_action" model="ir.actions.act_window">
        <field name="name">Phiếu feedback</field>
        <field name="res_model">th.feedback.ticket</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="th_feedback_ticket_tree_view"/>
        <!-- <field name="search_view_id" ref="th_feedback_ticket_search_view"/> -->
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>

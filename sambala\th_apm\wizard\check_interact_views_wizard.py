from odoo import models, fields, api, tools
from lxml import html


class ThCheckAllInteractWizardAPM(models.TransientModel):
    _inherit = "th.check.all.interact.wizard"

    def _domain_apm_th_user_ids(self):
        team = self.env['th.apm.team'].sudo().search([]).filtered(
            lambda t: self.env.uid in t.th_leader_ids.ids or self.env.uid in t.manager_id.ids)
        if team:
            return ['|', '|', ('id', 'in', team.mapped('th_member_ids').ids),
                    ('id', 'in', team.mapped('manager_id').ids), ('id', '=', self.env.uid)]
        else:
            return [('id', '=', self.env.uid)]

    th_apm_user_id = fields.Many2one(comodel_name='res.users', string='Nhân sự', domain=_domain_apm_th_user_ids)

    def th_apm_action_open_lognote_list(self):
        """ Mở list view chứa lognote theo điều kiện đã chọn """
        return {
            'type': 'ir.actions.act_window',
            'name': '<PERSON><PERSON>',
            'view_mode': 'tree',
            'res_model': 'mail.message',
            'domain': self.th_apm_get_domain_for_lognote(),
            'context': {'create': False, 'edit': False, 'delete': False},
            'view_id': self.env.ref('th_apm.th_apm_view_message_tree').id,
        }

    def th_apm_get_domain_for_lognote(self):
        """ Lấy domain cho list view (bỏ lọc theo ngày) """
        domain = [
            ('message_type', '=', 'comment'),
            ('model', '=', 'th.apm'),
        ]
        if self.th_apm_user_id:
            domain.append(('create_uid', '=', self.th_apm_user_id.id))
        return domain

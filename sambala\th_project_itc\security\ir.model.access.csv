id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_th_project_project_user_customer,access_th_project_project_user_customer,model_th_project_project,th_project_itc.th_group_project_user_customer,1,0,0,0
access_th_project_project_user,access_th_project_project_user,model_th_project_project,th_project_itc.th_group_project_user,1,1,0,0
access_th_project_project_manager,access_th_project_project_manager,model_th_project_project,th_project_itc.th_group_project_manager,1,1,1,1
access_th_project_project_stage,project.project_stage,model_th_project_project_stage,base.group_user,1,0,0,0
access_th_project_project_stage_manager,project.project_stage.manager,model_th_project_project_stage,th_project_itc.th_group_project_manager,1,1,1,1
access_th_project_task_type_user,project.task.type.user,model_th_project_task_type,base.group_user,1,0,0,0
access_th_project_task_type_project_user,project.task.type.project.user,model_th_project_task_type,th_project_itc.th_group_project_user,1,1,1,1
access_th_project_task_type_manager,project.task.type manager,model_th_project_task_type,th_project_itc.th_group_project_manager,1,1,1,1
access_th_project_task_type_portal,task_type_portal,model_th_project_task_type,base.group_portal,1,0,0,0
access_th_project_task,project.task,model_th_project_task,th_project_itc.th_group_project_user_customer,1,1,1,0
access_th_project_task_user,project.task.user,model_th_project_task,th_project_itc.th_group_project_user,1,1,1,1
access_th_report_project_task_user,report.project.task.user,model_th_report_project_task_user,th_project_itc.th_group_project_manager,1,1,1,1
access_th_report_project_task_user_project_user,report.project.task.user.project.user,model_th_report_project_task_user,th_project_itc.th_group_project_user,1,0,0,0
access_partner_task_user,base.res.partner user,base.model_res_partner,th_project_itc.th_group_project_user,1,0,0,0
access_task_on_partner,project.task on partners,model_th_project_task,base.group_user,1,0,0,0
access_task_portal,task_portal,th_project_itc.model_th_project_task,base.group_portal,1,0,0,0
access_th_project_user,project.project on partners,model_th_project_project,base.group_user,1,0,0,0
access_th_project_portal,project_portal,th_project_itc.model_th_project_project,base.group_portal,1,0,0,0
access_resource_calendar,project.resource_calendar user,resource.model_resource_calendar,th_project_itc.th_group_project_user,1,0,0,0
access_resource_calendar_attendance,project.resource_calendar_attendance user,resource.model_resource_calendar_attendance,th_project_itc.th_group_project_user,1,0,0,0
access_resource_calendar_leaves_user,resource.calendar.leaves user,resource.model_resource_calendar_leaves,th_project_itc.th_group_project_user,1,1,1,1
access_th_project_tags_all,project.project_tags_all,model_th_project_tags,,1,0,0,0
access_th_project_tags_manager,project.project_tags_manager,model_th_project_tags,th_project_itc.th_group_project_manager,1,1,1,1
access_th_project_tags_portal,project_tags_portal,th_project_itc.model_th_project_tags,base.group_portal,1,0,0,0
access_mail_activity_type_project_manager,mail.activity.type.project.manager,mail.model_mail_activity_type,th_project_itc.th_group_project_manager,1,1,1,1
access_account_analytic_account_user,account.analytic.account,analytic.model_account_analytic_account,th_project_itc.th_group_project_user,1,0,0,0
access_account_analytic_account_manager,account.analytic.account,analytic.model_account_analytic_account,th_project_itc.th_group_project_manager,1,1,1,1
access_account_analytic_line_project,account.analytic.line project,analytic.model_account_analytic_line,th_project_itc.th_group_project_manager,1,1,1,1
access_th_project_task_type_delete_wizard,th.project.task.type.delete.wizard,model_th_project_task_type_delete_wizard,th_project_itc.th_group_project_manager,1,1,1,1
access_th_project_task_recurrence,project.task.recurrence,model_th_project_task_recurrence,th_project_itc.th_group_project_user,1,1,1,1
project.access_project_task_burndown_chart_report,access_project_task_burndown_chart_report,th_project_itc.model_th_project_task_burndown_chart_report,th_project_itc.th_group_project_manager,1,1,1,1
project.access_project_task_burndown_chart_report_user,access_project_task_burndown_chart_report_user,th_project_itc.model_th_project_task_burndown_chart_report,th_project_itc.th_group_project_user,1,0,0,0
access_th_project_update_user,project.update.user,model_th_project_update,base.group_user,1,0,0,0
access_th_project_update_portal,project.update.portal,model_th_project_update,base.group_portal,0,0,0,0
access_th_project_update_project_user,project.update.project.user,model_th_project_update,th_project_itc.th_group_project_user,1,1,1,1
access_th_project_update_project_manager,project.update.project.manager,model_th_project_update,th_project_itc.th_group_project_manager,1,1,1,1

access_th_project_collaborator_manager,project.collaborator.manager,model_th_project_collaborator,th_project_itc.th_group_project_manager,1,1,1,1
access_th_project_collaborator_user,project.collaborator.user,model_th_project_collaborator,th_project_itc.th_group_project_user,1,0,0,0
access_th_project_collaborator_portal,project.collaborator.portal,model_th_project_collaborator,base.group_portal,1,0,0,0
access_th_project_share_manager,th.project.share.wizard.manager,model_th_project_share_wizard,th_project_itc.th_group_project_manager,1,1,1,0
access_th_project_personal_stage,project.personal.stage.user,model_th_project_task_stage_personal,base.group_user,1,1,1,1
access_th_project_export_wizard,th_project_export_wizard,model_report_th_project_export_wizard,base.group_user,1,1,1,1
access_model_th_project_link_sheet,model_th_project_link_sheet,model_th_project_link_sheet,base.group_user,1,1,1,1
access_th_timesheet_task,model_th_timesheet_task,model_th_timesheet_task,base.group_user,1,1,1,1
access_th_project_timesheet_wizard,model_th_project_timesheet_wizard,model_th_project_timesheet_wizard,base.group_user,1,1,1,1
access_th_project_team,th.project.team,model_th_project_team,,1,1,1,1
access_th_task_assign_wizard,th.task.assign,model_th_task_assign,th_project_itc.th_group_project_user,1,1,1,1
access_th_date_deadline,th.date.deadline,model_th_date_deadline,th_project_itc.th_group_project_user_customer,1,1,0,0
access_th_date_deadline_manager,th.date.deadline,model_th_date_deadline,th_project_itc.th_group_project_user,1,1,1,1
access_th_difficulty_level,th.difficulty.level,model_th_difficulty_level,,1,1,1,1

access_th_testcase,th.testcase,model_th_testcase,th_project_itc.th_group_project_user,1,0,0,0
access_th_testcase_manager,th.testcase,model_th_testcase,th_project_itc.th_group_project_testcase_manager,1,1,1,1

access_th_testcase_report_user,report.th.testcase.report,model_report_th_testcase_report,,1,1,1,1
access_th_testcase_report_detail,report.th.testcase.report.detail,model_report_th_testcase_report_detail,,1,1,1,1

access_th_testcase_customer,th.testcase,model_th_testcase,th_project_itc.th_group_project_user_customer,1,0,0,0

access_th_project_pull_request_user,th_project_pull_request_user,model_th_project_pull_request,th_project_itc.th_group_project_user,1,1,1,1
access_th_project_pull_request_customer,th_project_pull_request_user,model_th_project_pull_request,th_project_itc.th_group_project_user_customer,1,0,0,0
access_th_project_update_plan_user,access_th_project_update_plan_user,model_th_project_update_plan,th_project_itc.th_group_project_user,1,1,1,1

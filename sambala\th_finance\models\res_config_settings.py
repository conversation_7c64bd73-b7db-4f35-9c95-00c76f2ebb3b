from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    def _get_accountant_user_ids(self):
        return "[('id', 'in', %s)]" % self.env['res.users'].search(
            [('groups_id', '=', self.env.ref('th_finance.th_aum_accountant_group').id)]).ids

    th_accountant_receives_email_ids = fields.Many2many('res.users',
                                                        'th_custom_accountant_receives_email_rel',
                                                        domain=_get_accountant_user_ids,
                                                        string='Kế toán nhận email')

    def set_values(self):
        super().set_values()
        self.env['ir.config_parameter'].set_param('th_accountant_receives_email_ids',
                                                  self.th_accountant_receives_email_ids.ids)

    @api.model
    def get_values(self):
        res = super().get_values()
        th_accountant_receives_email_ids = self.env['ir.config_parameter'].sudo().get_param(
            'th_accountant_receives_email_ids')
        if th_accountant_receives_email_ids:
            res.update(th_accountant_receives_email_ids=[(6, 0, eval(th_accountant_receives_email_ids))])
        return res

from odoo import fields, models, api


class ThApm(models.Model):
    _inherit = "th.apm"

    def th_create_th_apm(self, datas):
        product = datas.get('th_product_ids', False)
        product_product = self.env['product.product'].search([('product_tmpl_id', '=', product)])
        datas['th_product_ids'] = product_product.ids
        apm_lead = super(ThApm, self).with_context(th_sync=True).create(datas)
        return apm_lead

    def th_write_th_apm(self, datas):
        product = datas.get('th_product_ids', False)
        product_product = self.env['product.product'].search([('product_tmpl_id', '=', product)])
        if product_product:
            datas['th_product_ids'] = product_product.ids
        apm_lead = super(ThApm, self).with_context(th_sync=True).write(datas)
        result = {'id': self.id}
        return result


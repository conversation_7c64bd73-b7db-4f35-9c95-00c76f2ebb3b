from typing import Annotated
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
import time
router = APIRouter(tags=["SRM"])


def write_log(self, data: object, state: str, duration: str, function_name: str = None, description: str = None):
    self.env['th.log.api'].create({
        'state': state,
        'th_model': str(self._name),
        'th_description': description,
        'th_input_data': str(data),
        'th_function_call': function_name,
        'is_log_fast_api': True,
        'th_fastapi_endpoint_id': self.id,
        'th_time_response': duration,
    })


@router.get("/{item_id}")
async def get_partners(fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)], env: Annotated[Environment, Depends(odoo_env)],
                       background_tasks: BackgroundTasks,
                       item_id: str):
    start = time.time()
    data = {}
    try:
        if fastapi:
            student = env['th.student'].th_get_student(item_id)
            partner_id = student.th_partner_id
            data = {
                'name': partner_id.name,
                'phone': partner_id.phone,
                'email': partner_id.email,
                'birthday': str(partner_id.th_birthday.strftime("%d/%m/%Y") if partner_id.th_birthday else ''),
                'student_code': student.th_student_code,
                'acceptance': student.th_acceptance,
                'major': student.th_major_id.name,
                'class': student.th_class,
                'fee_status': "Chưa hoàn thành" if student.th_fee_status == "undone" else "Hoàn thành",
                'gender': 'Nam' if student.th_gender == 'male' else 'Nữ' if student.th_gender == 'female' else 'Khác' if student.th_gender else '',
                'place_of_birth': partner_id.th_place_of_birth_id.name,
            }
            background_tasks.add_task(write_log, fastapi, data, 'success', str(round(time.time() - start, 4)), 'srm/get_partners')
            return data
        else:
            background_tasks.add_task(write_log, fastapi, data, 'error', str(round(time.time() - start, 4)), 'srm/get_partners', 'Không có quyền truy cập!')
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Không có quyền truy cập!"
            )
    except Exception as e:
        write_log(fastapi, data, 'error', str(round(time.time() - start, 4)), 'srm/get_partners', str(e))
        env.cr.commit()
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Có lỗi xảy ra vui lòng thử lại sau!"
        )

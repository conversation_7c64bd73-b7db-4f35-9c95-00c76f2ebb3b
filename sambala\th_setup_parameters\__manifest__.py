# -*- coding: utf-8 -*-
{
    'name': "ABS Setup Parameters",
    'summary': """ABS Setup Parameters""",
    'category': 'AUM Business System/ Setup Parameters',
    'description': """Long description of module's purpose""",
    'author': "ABS",
    'website': "http://www.yourcompany.com",
    'version': '16.0.260325',
    'depends': ['base',
                'th_contact',
                'web_domain_field',
                'mail',
                'sms',
                'product',
                'th_base',
                'sms',
                'calendar',
                'crm',
                'hr',
                'hr_recruitment',
                'payment',
                'planning',
                'project',
                'website_sms',
                ],
    'data': [
        'data/th_setup_data.xml',
        'data/action_scheduled.xml',
        'data/th_check_module.xml',
        'security/ir.model.access.csv',
        'views/th_major_view.xml',
        'views/th_university_view.xml',
        'views/th_status_detail.xml',
        'views/th_status_category_view.xml',
        'views/th_ownership_view.xml',
        'views/th_info_channel_view.xml',
        'views/th_graduation_system_view.xml',
        'views/th_combination_subjects_view.xml',
        'views/th_source_group_view.xml',
        'views/th_admissions_region_view.xml',
        'views/th_admissions_station_view.xml',
        'views/th_country_district_view.xml',
        'views/th_country_ward_view.xml',
        'views/th_ethnicity_view.xml',
        'views/th_product_pricelist_views.xml',
        'views/th_product_template_views.xml',
        'views/th_religion_view.xml',
        'views/res_partner.xml',
        'views/th_student_profile_view.xml',
        'views/th_module_view.xml',
        'views/th_product_category_view.xml',
        'views/res_config_settings.xml',
        'views/invisible_log.xml',
        'views/th_formio_view.xml',
        'views/th_log_api_view.xml',
        'views/th_menu.xml',
        'views/th_remove_phone_widget.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'th_setup_parameters/static/src/**/*',
        ],
    },
    'license': 'LGPL-3',
    'application': False,
}

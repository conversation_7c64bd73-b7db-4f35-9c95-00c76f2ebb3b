<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="0">
    <!-- <record id="th_odoo_trigger_partner_update_ontime" model="base.automation">
        <field name="name">Base Automation: Write Contact Partner Ontime</field>
        <field name="model_id" ref="model_res_partner"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_update_contact(record, 'update', th_type_sync='ontime')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('base.field_res_partner__name'),
            ref('base.field_res_partner__phone'),
            ref('th_setup_parameters.field_res_partner__th_phone2'),
            ref('base.field_res_partner__email'),
        ])]" />
        <field name="trigger">on_create</field>
        <field name="active">1</field>
    </record> -->

    <record id="th_odoo_trigger_partner_update_an_hour" model="base.automation">
        <field name="name">Base Automation: Write Contact Partner An Hour</field>
        <field name="model_id" ref="model_res_partner"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_update_contact(record, 'update', th_type_sync='an_hour')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('base.field_res_partner__name'),
            ref('base.field_res_partner__phone'),
            ref('th_setup_parameters.field_res_partner__th_phone2'),
            ref('base.field_res_partner__email'),
            ref('th_setup_parameters.field_res_partner__th_customer_code'),
            ref('th_setup_parameters.field_res_partner__th_gender'),
            ref('th_setup_parameters.field_res_partner__th_birthday'),
            ref('th_setup_parameters.field_res_partner__th_place_of_birth_id'),
            ref('base.field_res_partner__title'),
            ref('base.field_res_partner__lang'),
            ref('base.field_res_partner__function'),
            ref('th_setup_parameters.field_res_partner__th_citizen_identification'),
            ref('th_setup_parameters.field_res_partner__th_date_identification'),
            ref('th_setup_parameters.field_res_partner__th_place_identification'),
            ref('base.field_res_partner__vat'),
            ref('base.field_res_partner__street'),
            ref('th_setup_parameters.field_res_partner__th_ward_id'),
            ref('th_setup_parameters.field_res_partner__th_district_id'),
            ref('base.field_res_partner__state_id'),
            ref('base.field_res_partner__country_id'),
            ref('th_setup_parameters.field_res_partner__th_street'),
            ref('th_setup_parameters.field_res_partner__th_ward_permanent_id'),
            ref('th_setup_parameters.field_res_partner__th_district_permanent_id'),
            ref('th_setup_parameters.field_res_partner__th_state_id'),
            ref('th_setup_parameters.field_res_partner__th_country_id'),
            ref('th_setup_parameters.field_res_partner__th_ethnicity_id'),
            ref('th_setup_parameters.field_res_partner__th_religion_id'),
            ref('th_setup_parameters.field_res_partner__th_module_ids'),
            ref('th_apm.field_res_partner__th_apm_contact_trait_ids'),
        ])]" />
        <field name="trigger">on_write</field>
        <field name="active">1</field>
    </record>
</odoo>
# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import date


class AccountMove(models.Model):
    _inherit = 'account.move'

    th_account_payment_id = fields.Many2one('th.account.payment',
                                            string='Thu chi theo hoá đơn')
    th_internal_transfer_id = fields.Many2one('th.internal.transfer',
                                              string='Chuyển quỹ nội bộ')
    th_receipt_expenditure_book_ids = fields.One2many('th.receipt.expenditure.book',
                                                      'th_account_move_id',
                                                      string='Sổ thu chi')
    th_internal_move_type = fields.Selection([('out_internal', 'Hóa đơn thu nội bộ'),
                                              ('in_internal', 'Hóa đơn chi nội bộ')],
                                             string="Phân loại nội bộ")
    payment_state = fields.Selection(selection_add=[('over_payment', 'Thanh toán dư')],
                                     ondelete={'over_payment': 'cascade'},
                                     tracking=False)
    th_amount_excess = fields.Monetary(string="Số tiền trả thừa",
                                       compute='_compute_amount_excess_and_final_payment_date',
                                       store=True,
                                       precompute=False)
    partner_shipping_id = fields.Many2one(string="Địa chỉ giao hàng",
                                          tracking=True)
    invoice_date = fields.Date(default=fields.Date.today(),
                               string="Ngày tạo hóa đơn")
    invoice_date_due = fields.Date(string="Ngày hoàn tất thanh toán",
                                   compute='_compute_invoice_date_due',
                                   store=True,
                                   states={'draft': [('readonly', False)]},
                                   index=True,
                                   copy=False)
    th_final_payment_date = fields.Date(string="Ngày thanh toán",
                                        compute='_compute_amount_excess_and_final_payment_date',
                                        store=True,
                                        precompute=False)
    th_payment_status = fields.Selection([('not_paid', 'Chưa thanh toán'),
                                          ('partial', 'Thanh toán một phần'),
                                          ('paid', 'Thanh toán đầy đủ'),
                                          ('over_payment', 'Thanh toán dư')],
                                         string="Trạng thái thu chi",
                                         compute='_compute_th_payment_status',
                                         store=True,
                                         precompute=False,
                                         tracking=True)

    # Kế thừa nút Đưa về dự thảo của base
    def button_draft(self):
        for record in self:
            if record.env.company.fiscalyear_lock_date and record.date <= record.env.company.fiscalyear_lock_date:
                raise UserError(
                    'Đã khoá kỳ kế toán từ ngày %s , bạn không thể đưa về dự thảo bút toán trước ngày khoá.',
                    record.env.company.fiscalyear_lock_date.strftime("%d/%m/%Y"))
            if (record.th_account_payment_id != None and record.th_account_payment_id.th_state == 'posted') or (
                    record.th_internal_transfer_id != None and record.th_internal_transfer_id.th_state == 'posted'):
                raise UserError(('Bạn không thể đưa về dự thảo bút toán của phiếu: %s') % record.ref)
            if record.th_receipt_expenditure_book_ids.filtered(lambda line: line.th_link_state == True):
                raise UserError('Vui lòng hủy liên kết Sổ thu chi trước khi Đưa về dự thảo!')
        self.line_ids.filtered(lambda
                                   line: line.sale_line_ids.is_downpayment and not line.sale_line_ids.display_type).sale_line_ids._compute_name()
        res = super().button_draft()
        return res

    # Ghi dè hàm của account move để cho phép xóa sổ bút toán trước
    @api.ondelete(at_uninstall=False)
    def _unlink_forbid_parts_of_chain(self):
        return True

    # Logic thay đổi số tiền thừa, ngày thanh toán và trạng thái thanh toán của base (hiện tại không dùng)
    # @api.depends('amount_residual', 'payment_state', 'line_ids.payment_id')
    def _compute_amount_excess_and_final_payment_date(self):
        for move in self:
            if move.move_type not in ('entry', 'out_refund', 'in_refund') and move.amount_total != 0:
                if move.payment_state == 'not_paid':
                    move.th_amount_excess = 0
                    move.th_final_payment_date = False
                    return
                payment_entry = self.search([('ref', 'ilike', move.name),
                                             ('ref', 'not ilike', 'Reversal'),
                                             ('ref', 'not ilike', 'Đảo ngược'),
                                             ('move_type', 'not in', ('out_refund', 'in_refund')),
                                             ('state', '=', 'posted')])

                total_paid = sum(payment_entry.mapped('amount_total'))
                if total_paid > move.amount_total:
                    move.th_amount_excess = total_paid - move.amount_total
                    if move.th_amount_excess > 0 and move.amount_residual == 0:
                        move.payment_state = 'over_payment'
                    elif move.th_amount_excess > 0 and move.amount_residual > 0:
                        move.payment_state = 'partial'
                        move.th_amount_excess = 0
                        if move.amount_residual == move.amount_total:
                            move.payment_state = 'not_paid'
                else:
                    move.th_amount_excess = 0
                    if move.payment_state == 'in_payment':
                        move.update({'payment_state': 'paid'})
                        move.update({'payment_state': 'paid'})

                all_payment_date = payment_entry.mapped('date')
                move.th_final_payment_date = max(all_payment_date) if all_payment_date else None
                if move.payment_state in ('not_paid', 'partial'):
                    move.th_final_payment_date = False

    # Logic khi thay đổi Sổ thu chi
    @api.depends('th_receipt_expenditure_book_ids', 'th_receipt_expenditure_book_ids.th_link_state',
                 'th_receipt_expenditure_book_ids.th_amount', 'state')
    def _compute_th_payment_status(self):
        for move in self:
            # Ngắt logic khi ấn tạo mới phiếu
            if not move.id:
                return
            if move.state == 'posted':
                # Tự động thanh toán với hóa đơn 0 đồng
                if not move.th_receipt_expenditure_book_ids.filtered(
                        lambda line: line.th_link_state == True) and move.amount_total == 0:
                    move.th_payment_status = 'paid'
                    move.th_final_payment_date = date.today()
                    return
                # Logic khi có Sổ thu chi liên kết với hóa đơn
                sale_order_id = self.env['sale.order'].search([('invoice_ids', 'in', move.ids)], limit=1)
                if move.th_receipt_expenditure_book_ids:
                    # Update loại phiếu thu hoặc chi dựa theo loại Hóa đơn vào sổ thu chi đã liên kết
                    if move.move_type in ('out_invoice', 'out_refund', 'out_receipt'):
                        move.th_receipt_expenditure_book_ids.update({'th_payment_type': 'inbound'})
                    elif move.move_type in ('in_invoice', 'in_refund', 'in_receipt'):
                        move.th_receipt_expenditure_book_ids.update({'th_payment_type': 'outbound'})
                    # Update đánh dấu giao dịch nội bộ dựa theo Hóa đơn nội bộ vào sổ thu chi đã liên kết
                    if move.th_internal_move_type:
                        move.th_receipt_expenditure_book_ids.update({'th_is_internal': True})
                    else:
                        move.th_receipt_expenditure_book_ids.update({'th_is_internal': False})
                    # Tính tổng tiền phiếu thu chi đã liên kết và gán giá trị
                    total_linked_amount = sum(move.th_receipt_expenditure_book_ids.filtered(
                        lambda line: line.th_link_state == True).mapped('th_amount'))
                    # Lấy ngày thanh toán phiếu thu chi đã liên kết
                    all_payment_date = move.th_receipt_expenditure_book_ids.filtered(
                        lambda line: line.th_link_state == True).mapped('th_accounting_date')

                    if total_linked_amount == 0:
                        move.th_payment_status = 'not_paid'
                        move.amount_residual = move.amount_total
                    elif total_linked_amount < move.amount_total:
                        move.th_payment_status = 'partial'
                        move.amount_residual = move.amount_total - total_linked_amount
                    elif total_linked_amount > move.amount_total:
                        move.th_payment_status = 'over_payment'
                        move.amount_residual = 0
                    elif total_linked_amount == move.amount_total:
                        move.th_payment_status = 'paid'
                        move.amount_residual = 0

                    if move.th_payment_status in ('not_paid', 'partial'):
                        move.th_amount_excess = 0
                        move.th_final_payment_date = False
                    elif move.th_payment_status in ('paid', 'over_payment'):
                        move.amount_residual = 0
                        move.th_amount_excess = total_linked_amount - move.amount_total
                        move.th_final_payment_date = max(all_payment_date) if all_payment_date else None
                else:
                    move.th_payment_status = 'not_paid'
                    move.amount_residual = move.amount_total
                    move.th_amount_excess = 0
                    move.th_final_payment_date = False

    # Button tạo phiếu thu chi trong form Hóa đơn
    def th_action_register_receipt_expenditure(self):
        th_payment_type = False
        if self.move_type in ('out_invoice', 'out_refund', 'out_receipt'):
            th_payment_type = 'inbound'
        elif self.move_type in ('in_invoice', 'in_refund', 'in_receipt'):
            th_payment_type = 'outbound'

        if self.th_internal_move_type:
            th_is_internal = True
        else:
            th_is_internal = False
        return {
            'name': 'Tạo phiếu thu chi',
            'res_model': 'th.receipt.expenditure.book',
            'view_mode': 'form',
            'views': [(self.env.ref('th_finance.th_receipt_expenditure_register_form').id, 'form')],
            'target': 'new',
            'type': 'ir.actions.act_window',
            'context': {
                'default_th_account_move_id': self.id,
                'default_th_bill_date': date.today(),
                'default_th_amount': self.amount_residual,
                'default_th_payment_type': th_payment_type,
                'default_th_is_internal': th_is_internal,
            },
        }

    # Loại bút toán ra khỏi logic import ID account_move
    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        args.append(('move_type', '!=', 'entry'))
        return super(AccountMove, self).name_search(name, args=args, operator=operator, limit=limit)

    # Query update trạng thái thu chi mới từ trạng thái thanh toán của base
    def change_th_payment_status_from_base_payment_state(self):
        self.env.cr.execute("""
                            UPDATE account_move
                            SET th_payment_status = 'not_paid'
                            WHERE payment_state = 'not_paid';
                            UPDATE account_move
                            SET th_payment_status = 'partial'
                            WHERE payment_state = 'partial';
                            UPDATE account_move
                            SET th_payment_status = 'paid'
                            WHERE payment_state = 'paid';
                            UPDATE account_move
                            SET th_payment_status = 'over_payment'
                            WHERE payment_state = 'over_payment';
                            """)

    @api.depends('needed_terms', 'payment_state')
    def _compute_invoice_date_due(self):
        for move in self:
            if move.payment_state in ('paid', 'over_payment'):
                move.invoice_date_due = date.today()

    def th_action_send_mail_invoice(self):
        self.ensure_one()  # Đảm bảo chỉ thao tác trên 1 bản ghi
        # Lấy thông tin kế toán viên đã cấu hình trong thiết lập
        accountants = self.env['res.users'].sudo().search([('id', 'in', eval(
            self.env['ir.config_parameter'].sudo().get_param('th_accountant_receives_email_ids')))])
        # Lấy email của khách hàng trong hóa đơn
        partner_email = self.partner_id.email or "Không xác định"
        invoice_name = self.name or "Không xác định"
        url_invoice = self.get_base_url() + "/web#id=" + str(self.id) + "&model=" + self._name
        subject = "Yêu cầu xuất hóa đơn"
        body = """
            <div class="container">
                <div class="header">
                  <p>Kính gửi <strong>%s</strong>,</p>
                </div>
                <div class="content">
                  <p>Khách hàng <strong>%s</strong> có yêu cầu xuất hóa đơn cho <strong>%s</strong>.</p>
                  <p><a href="%s" target="_blank" style="display: inline-block; padding: 10px 20px; background-color: #007BFF; color: #fff; text-decoration: none; border-radius: 4px; font-family: Arial, sans-serif;">Xem hóa đơn</a></p>
                </div>
                <div class="footer">
                  <p>Trân trọng,</p>
                  <p>%s.</p>
                </div>
            </div>
        """ % (", ".join(accountants.mapped('name')), self.partner_id.name,
               invoice_name, url_invoice, self.partner_id.name)

        mail_values = {
            'subject': subject,
            'body_html': "<body><div>%s</div></body>" % body,
            'email_from': partner_email,
            'email_to': ", ".join(filter(None, accountants.mapped('email'))) or "Không xác định",
        }

        # Tạo và gửi mail
        mail = self.env['mail.mail'].create(mail_values)
        mail.send()  # Gửi mail
        return True
